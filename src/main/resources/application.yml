server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: sentiment-monitor
  
  # Database Configuration
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  # JPA Configuration
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.dialect.H2Dialect
  
  # H2 Console (for development)
  h2:
    console:
      enabled: true
      path: /h2-console

# Logging Configuration
logging:
  level:
    com.example.demo: DEBUG
    org.springframework.web: INFO
    org.hibernate.SQL: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/sentiment-monitor.log

# Custom Configuration
sentiment:
  monitor:
    # MediaCrawler Integration
    mediacrawler:
      base-url: "http://localhost:8000"  # MediaCrawler服务地址
      timeout: 30000
      retry-count: 3
    
    # Monitoring Configuration
    platforms:
      - name: "xiaohongshu"
        enabled: true
        interval: 300  # 5 minutes
      - name: "douyin"
        enabled: true
        interval: 600  # 10 minutes
      - name: "weibo"
        enabled: true
        interval: 300  # 5 minutes
      - name: "bilibili"
        enabled: false
        interval: 900  # 15 minutes
    
    # Alert Configuration
    alert:
      enabled: true
      threshold:
        negative-sentiment: 0.7  # 负面情感阈值
        mention-count: 100       # 提及次数阈值
      notification:
        email: true
        webhook: true
    
    # Keywords to monitor
    keywords:
      - "品牌名称"
      - "产品名称"
      - "竞品分析"
    
    # Sentiment Analysis
    sentiment-analysis:
      enabled: true
      provider: "local"  # local, baidu, tencent
      confidence-threshold: 0.6

# Management Endpoints
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
