package com.example.demo.controller;

import com.example.demo.entity.SentimentData;
import com.example.demo.repository.SentimentDataRepository;
import com.example.demo.service.DataCollectionService;
import com.example.demo.service.SentimentAnalysisService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 舆情数据控制器
 */
@RestController
@RequestMapping("/sentiment")
@RequiredArgsConstructor
@Slf4j
public class SentimentController {
    
    private final SentimentDataRepository sentimentDataRepository;
    private final DataCollectionService dataCollectionService;
    private final SentimentAnalysisService sentimentAnalysisService;
    
    /**
     * 获取舆情数据列表
     */
    @GetMapping("/data")
    public ResponseEntity<Page<SentimentData>> getSentimentData(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "crawlTime") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir,
            @RequestParam(required = false) String platform,
            @RequestParam(required = false) String sentiment,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        
        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);
        
        // 这里简化实现，实际项目中应该使用Specification或自定义查询
        Page<SentimentData> result;
        if (platform != null && sentiment != null) {
            // 需要实现复合查询
            result = sentimentDataRepository.findAll(pageable);
        } else if (platform != null) {
            result = sentimentDataRepository.findAll(pageable);
        } else {
            result = sentimentDataRepository.findAll(pageable);
        }
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 根据ID获取舆情数据
     */
    @GetMapping("/data/{id}")
    public ResponseEntity<SentimentData> getSentimentDataById(@PathVariable Long id) {
        Optional<SentimentData> data = sentimentDataRepository.findById(id);
        return data.map(ResponseEntity::ok)
                  .orElse(ResponseEntity.notFound().build());
    }
    
    /**
     * 根据关键词搜索
     */
    @GetMapping("/search")
    public ResponseEntity<List<SentimentData>> searchByKeyword(@RequestParam String keyword) {
        List<SentimentData> results = sentimentDataRepository.findByKeyword(keyword);
        return ResponseEntity.ok(results);
    }
    
    /**
     * 获取情感分布统计
     */
    @GetMapping("/stats/sentiment")
    public ResponseEntity<Map<String, Object>> getSentimentStats(
            @RequestParam(required = false) String platform,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        
        List<Object[]> stats;
        if (startTime != null && endTime != null) {
            stats = sentimentDataRepository.countSentimentByTimeRange(startTime, endTime);
        } else if (platform != null) {
            stats = sentimentDataRepository.countSentimentByPlatform(platform);
        } else {
            // 默认查询最近24小时
            LocalDateTime now = LocalDateTime.now();
            stats = sentimentDataRepository.countSentimentByTimeRange(now.minusHours(24), now);
        }
        
        Map<String, Long> sentimentCounts = stats.stream()
                .collect(java.util.stream.Collectors.toMap(
                        arr -> (String) arr[0],
                        arr -> (Long) arr[1]
                ));
        
        long total = sentimentCounts.values().stream().mapToLong(Long::longValue).sum();
        
        return ResponseEntity.ok(Map.of(
                "counts", sentimentCounts,
                "total", total,
                "percentages", sentimentCounts.entrySet().stream()
                        .collect(java.util.stream.Collectors.toMap(
                                Map.Entry::getKey,
                                entry -> total > 0 ? (entry.getValue() * 100.0 / total) : 0.0
                        ))
        ));
    }
    
    /**
     * 获取热门内容
     */
    @GetMapping("/hot")
    public ResponseEntity<List<SentimentData>> getHotContent(
            @RequestParam(defaultValue = "24") int hours,
            @RequestParam(defaultValue = "10") int limit) {
        
        LocalDateTime startTime = LocalDateTime.now().minusHours(hours);
        LocalDateTime endTime = LocalDateTime.now();
        
        List<SentimentData> hotContent = sentimentDataRepository
                .findHotContentByTimeRange(startTime, endTime)
                .stream()
                .limit(limit)
                .toList();
        
        return ResponseEntity.ok(hotContent);
    }
    
    /**
     * 手动采集数据
     */
    @PostMapping("/collect")
    public ResponseEntity<Map<String, Object>> collectData(
            @RequestParam String platform,
            @RequestParam List<String> keywords,
            @RequestParam(defaultValue = "50") int limit) {
        
        try {
            dataCollectionService.collectDataManually(platform, keywords, limit);
            return ResponseEntity.ok(Map.of(
                    "status", "success",
                    "message", "数据采集任务已启动",
                    "platform", platform,
                    "keywords", keywords,
                    "limit", limit
            ));
        } catch (Exception e) {
            log.error("手动采集数据失败：{}", e.getMessage(), e);
            return ResponseEntity.badRequest().body(Map.of(
                    "status", "error",
                    "message", e.getMessage()
            ));
        }
    }
    
    /**
     * 分析文本情感
     */
    @PostMapping("/analyze")
    public ResponseEntity<Map<String, Object>> analyzeSentiment(@RequestBody Map<String, String> request) {
        String text = request.get("text");
        if (text == null || text.trim().isEmpty()) {
            return ResponseEntity.badRequest().body(Map.of(
                    "status", "error",
                    "message", "文本内容不能为空"
            ));
        }
        
        try {
            var result = sentimentAnalysisService.analyzeSentiment(text);
            return ResponseEntity.ok(Map.of(
                    "status", "success",
                    "result", result
            ));
        } catch (Exception e) {
            log.error("情感分析失败：{}", e.getMessage(), e);
            return ResponseEntity.badRequest().body(Map.of(
                    "status", "error",
                    "message", e.getMessage()
            ));
        }
    }
    
    /**
     * 获取平台统计
     */
    @GetMapping("/stats/platform")
    public ResponseEntity<Map<String, Long>> getPlatformStats() {
        List<SentimentData> allData = sentimentDataRepository.findAll();
        
        Map<String, Long> platformStats = allData.stream()
                .collect(java.util.stream.Collectors.groupingBy(
                        SentimentData::getPlatform,
                        java.util.stream.Collectors.counting()
                ));
        
        return ResponseEntity.ok(platformStats);
    }
    
    /**
     * 删除舆情数据
     */
    @DeleteMapping("/data/{id}")
    public ResponseEntity<Map<String, String>> deleteSentimentData(@PathVariable Long id) {
        try {
            if (sentimentDataRepository.existsById(id)) {
                sentimentDataRepository.deleteById(id);
                return ResponseEntity.ok(Map.of(
                        "status", "success",
                        "message", "数据删除成功"
                ));
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            log.error("删除数据失败：{}", e.getMessage(), e);
            return ResponseEntity.badRequest().body(Map.of(
                    "status", "error",
                    "message", e.getMessage()
            ));
        }
    }
}
