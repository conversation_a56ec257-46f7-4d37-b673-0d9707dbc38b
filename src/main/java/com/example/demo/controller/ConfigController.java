package com.example.demo.controller;

import com.example.demo.entity.AlertRule;
import com.example.demo.entity.MonitorConfig;
import com.example.demo.repository.AlertRuleRepository;
import com.example.demo.repository.MonitorConfigRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 配置管理控制器
 */
@RestController
@RequestMapping("/config")
@RequiredArgsConstructor
@Slf4j
public class ConfigController {
    
    private final MonitorConfigRepository monitorConfigRepository;
    private final AlertRuleRepository alertRuleRepository;
    
    // ========== 监控配置管理 ==========
    
    /**
     * 获取所有监控配置
     */
    @GetMapping("/monitor")
    public ResponseEntity<List<MonitorConfig>> getAllMonitorConfigs() {
        List<MonitorConfig> configs = monitorConfigRepository.findAll();
        return ResponseEntity.ok(configs);
    }
    
    /**
     * 根据ID获取监控配置
     */
    @GetMapping("/monitor/{id}")
    public ResponseEntity<MonitorConfig> getMonitorConfigById(@PathVariable Long id) {
        Optional<MonitorConfig> config = monitorConfigRepository.findById(id);
        return config.map(ResponseEntity::ok)
                    .orElse(ResponseEntity.notFound().build());
    }
    
    /**
     * 创建监控配置
     */
    @PostMapping("/monitor")
    public ResponseEntity<MonitorConfig> createMonitorConfig(@RequestBody MonitorConfig config) {
        try {
            // 设置创建时间和下次执行时间
            LocalDateTime now = LocalDateTime.now();
            config.setCreatedTime(now);
            config.setUpdatedTime(now);
            config.setNextExecutionTime(now.plusSeconds(config.getIntervalSeconds()));
            
            MonitorConfig savedConfig = monitorConfigRepository.save(config);
            log.info("创建监控配置：{}", savedConfig.getName());
            
            return ResponseEntity.ok(savedConfig);
        } catch (Exception e) {
            log.error("创建监控配置失败：{}", e.getMessage(), e);
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * 更新监控配置
     */
    @PutMapping("/monitor/{id}")
    public ResponseEntity<MonitorConfig> updateMonitorConfig(
            @PathVariable Long id, @RequestBody MonitorConfig config) {
        try {
            Optional<MonitorConfig> existingConfig = monitorConfigRepository.findById(id);
            if (existingConfig.isEmpty()) {
                return ResponseEntity.notFound().build();
            }
            
            config.setId(id);
            config.setCreatedTime(existingConfig.get().getCreatedTime());
            config.setUpdatedTime(LocalDateTime.now());
            
            // 如果间隔时间改变，更新下次执行时间
            if (!config.getIntervalSeconds().equals(existingConfig.get().getIntervalSeconds())) {
                config.setNextExecutionTime(LocalDateTime.now().plusSeconds(config.getIntervalSeconds()));
            }
            
            MonitorConfig savedConfig = monitorConfigRepository.save(config);
            log.info("更新监控配置：{}", savedConfig.getName());
            
            return ResponseEntity.ok(savedConfig);
        } catch (Exception e) {
            log.error("更新监控配置失败：{}", e.getMessage(), e);
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * 删除监控配置
     */
    @DeleteMapping("/monitor/{id}")
    public ResponseEntity<Map<String, String>> deleteMonitorConfig(@PathVariable Long id) {
        try {
            if (monitorConfigRepository.existsById(id)) {
                monitorConfigRepository.deleteById(id);
                log.info("删除监控配置：{}", id);
                return ResponseEntity.ok(Map.of(
                        "status", "success",
                        "message", "监控配置删除成功"
                ));
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            log.error("删除监控配置失败：{}", e.getMessage(), e);
            return ResponseEntity.badRequest().body(Map.of(
                    "status", "error",
                    "message", e.getMessage()
            ));
        }
    }
    
    /**
     * 启用/禁用监控配置
     */
    @PatchMapping("/monitor/{id}/toggle")
    public ResponseEntity<MonitorConfig> toggleMonitorConfig(@PathVariable Long id) {
        try {
            Optional<MonitorConfig> configOpt = monitorConfigRepository.findById(id);
            if (configOpt.isEmpty()) {
                return ResponseEntity.notFound().build();
            }
            
            MonitorConfig config = configOpt.get();
            config.setIsEnabled(!config.getIsEnabled());
            config.setUpdatedTime(LocalDateTime.now());
            
            if (config.getIsEnabled()) {
                // 重新启用时，设置下次执行时间
                config.setNextExecutionTime(LocalDateTime.now().plusSeconds(config.getIntervalSeconds()));
            }
            
            MonitorConfig savedConfig = monitorConfigRepository.save(config);
            log.info("切换监控配置状态：{}，启用：{}", savedConfig.getName(), savedConfig.getIsEnabled());
            
            return ResponseEntity.ok(savedConfig);
        } catch (Exception e) {
            log.error("切换监控配置状态失败：{}", e.getMessage(), e);
            return ResponseEntity.badRequest().build();
        }
    }
    
    // ========== 预警规则管理 ==========
    
    /**
     * 获取所有预警规则
     */
    @GetMapping("/alert")
    public ResponseEntity<List<AlertRule>> getAllAlertRules() {
        List<AlertRule> rules = alertRuleRepository.findAll();
        return ResponseEntity.ok(rules);
    }
    
    /**
     * 根据ID获取预警规则
     */
    @GetMapping("/alert/{id}")
    public ResponseEntity<AlertRule> getAlertRuleById(@PathVariable Long id) {
        Optional<AlertRule> rule = alertRuleRepository.findById(id);
        return rule.map(ResponseEntity::ok)
                  .orElse(ResponseEntity.notFound().build());
    }
    
    /**
     * 创建预警规则
     */
    @PostMapping("/alert")
    public ResponseEntity<AlertRule> createAlertRule(@RequestBody AlertRule rule) {
        try {
            LocalDateTime now = LocalDateTime.now();
            rule.setCreatedTime(now);
            rule.setUpdatedTime(now);
            rule.setTriggerCount(0);
            
            AlertRule savedRule = alertRuleRepository.save(rule);
            log.info("创建预警规则：{}", savedRule.getName());
            
            return ResponseEntity.ok(savedRule);
        } catch (Exception e) {
            log.error("创建预警规则失败：{}", e.getMessage(), e);
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * 更新预警规则
     */
    @PutMapping("/alert/{id}")
    public ResponseEntity<AlertRule> updateAlertRule(@PathVariable Long id, @RequestBody AlertRule rule) {
        try {
            Optional<AlertRule> existingRule = alertRuleRepository.findById(id);
            if (existingRule.isEmpty()) {
                return ResponseEntity.notFound().build();
            }
            
            rule.setId(id);
            rule.setCreatedTime(existingRule.get().getCreatedTime());
            rule.setUpdatedTime(LocalDateTime.now());
            rule.setTriggerCount(existingRule.get().getTriggerCount());
            rule.setLastTriggeredTime(existingRule.get().getLastTriggeredTime());
            
            AlertRule savedRule = alertRuleRepository.save(rule);
            log.info("更新预警规则：{}", savedRule.getName());
            
            return ResponseEntity.ok(savedRule);
        } catch (Exception e) {
            log.error("更新预警规则失败：{}", e.getMessage(), e);
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * 删除预警规则
     */
    @DeleteMapping("/alert/{id}")
    public ResponseEntity<Map<String, String>> deleteAlertRule(@PathVariable Long id) {
        try {
            if (alertRuleRepository.existsById(id)) {
                alertRuleRepository.deleteById(id);
                log.info("删除预警规则：{}", id);
                return ResponseEntity.ok(Map.of(
                        "status", "success",
                        "message", "预警规则删除成功"
                ));
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            log.error("删除预警规则失败：{}", e.getMessage(), e);
            return ResponseEntity.badRequest().body(Map.of(
                    "status", "error",
                    "message", e.getMessage()
            ));
        }
    }
    
    /**
     * 启用/禁用预警规则
     */
    @PatchMapping("/alert/{id}/toggle")
    public ResponseEntity<AlertRule> toggleAlertRule(@PathVariable Long id) {
        try {
            Optional<AlertRule> ruleOpt = alertRuleRepository.findById(id);
            if (ruleOpt.isEmpty()) {
                return ResponseEntity.notFound().build();
            }
            
            AlertRule rule = ruleOpt.get();
            rule.setIsEnabled(!rule.getIsEnabled());
            rule.setUpdatedTime(LocalDateTime.now());
            
            AlertRule savedRule = alertRuleRepository.save(rule);
            log.info("切换预警规则状态：{}，启用：{}", savedRule.getName(), savedRule.getIsEnabled());
            
            return ResponseEntity.ok(savedRule);
        } catch (Exception e) {
            log.error("切换预警规则状态失败：{}", e.getMessage(), e);
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * 重置预警规则触发计数
     */
    @PatchMapping("/alert/{id}/reset")
    public ResponseEntity<AlertRule> resetAlertRule(@PathVariable Long id) {
        try {
            Optional<AlertRule> ruleOpt = alertRuleRepository.findById(id);
            if (ruleOpt.isEmpty()) {
                return ResponseEntity.notFound().build();
            }
            
            AlertRule rule = ruleOpt.get();
            rule.setTriggerCount(0);
            rule.setLastTriggeredTime(null);
            rule.setUpdatedTime(LocalDateTime.now());
            
            AlertRule savedRule = alertRuleRepository.save(rule);
            log.info("重置预警规则触发计数：{}", savedRule.getName());
            
            return ResponseEntity.ok(savedRule);
        } catch (Exception e) {
            log.error("重置预警规则失败：{}", e.getMessage(), e);
            return ResponseEntity.badRequest().build();
        }
    }
}
