package com.example.demo.controller;

import com.example.demo.repository.AlertRuleRepository;
import com.example.demo.repository.MonitorConfigRepository;
import com.example.demo.repository.SentimentDataRepository;
import com.example.demo.service.MediaCrawlerClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 系统状态控制器
 */
@RestController
@RequestMapping("/system")
@RequiredArgsConstructor
@Slf4j
public class SystemController {
    
    private final SentimentDataRepository sentimentDataRepository;
    private final MonitorConfigRepository monitorConfigRepository;
    private final AlertRuleRepository alertRuleRepository;
    private final MediaCrawlerClient mediaCrawlerClient;
    
    /**
     * 系统健康检查
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> health = new HashMap<>();
        
        try {
            // 检查数据库连接
            long totalRecords = sentimentDataRepository.count();
            health.put("database", Map.of(
                    "status", "UP",
                    "total_records", totalRecords
            ));
            
            // 检查MediaCrawler连接
            Boolean mediaCrawlerHealth = mediaCrawlerClient.checkHealth().block();
            health.put("mediacrawler", Map.of(
                    "status", Boolean.TRUE.equals(mediaCrawlerHealth) ? "UP" : "DOWN"
            ));
            
            health.put("status", "UP");
            health.put("timestamp", LocalDateTime.now());
            
            return ResponseEntity.ok(health);
            
        } catch (Exception e) {
            log.error("健康检查失败：{}", e.getMessage(), e);
            health.put("status", "DOWN");
            health.put("error", e.getMessage());
            health.put("timestamp", LocalDateTime.now());
            
            return ResponseEntity.status(503).body(health);
        }
    }
    
    /**
     * 系统统计信息
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getSystemStats() {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            // 数据统计
            long totalData = sentimentDataRepository.count();
            long todayData = sentimentDataRepository
                    .findByCrawlTimeBetween(
                            LocalDateTime.now().toLocalDate().atStartOfDay(),
                            LocalDateTime.now()
                    ).size();
            
            stats.put("data", Map.of(
                    "total", totalData,
                    "today", todayData
            ));
            
            // 配置统计
            long totalConfigs = monitorConfigRepository.count();
            long enabledConfigs = monitorConfigRepository.findByIsEnabledTrue().size();
            
            stats.put("monitor_configs", Map.of(
                    "total", totalConfigs,
                    "enabled", enabledConfigs
            ));
            
            // 预警规则统计
            long totalRules = alertRuleRepository.count();
            long enabledRules = alertRuleRepository.findByIsEnabledTrue().size();
            
            stats.put("alert_rules", Map.of(
                    "total", totalRules,
                    "enabled", enabledRules
            ));
            
            // 最近24小时情感分布
            LocalDateTime yesterday = LocalDateTime.now().minusHours(24);
            var sentimentStats = sentimentDataRepository
                    .countSentimentByTimeRange(yesterday, LocalDateTime.now());
            
            Map<String, Long> sentimentCounts = new HashMap<>();
            for (Object[] stat : sentimentStats) {
                sentimentCounts.put((String) stat[0], (Long) stat[1]);
            }
            
            stats.put("sentiment_distribution_24h", sentimentCounts);
            
            stats.put("timestamp", LocalDateTime.now());
            
            return ResponseEntity.ok(stats);
            
        } catch (Exception e) {
            log.error("获取系统统计失败：{}", e.getMessage(), e);
            return ResponseEntity.status(500).body(Map.of(
                    "error", e.getMessage(),
                    "timestamp", LocalDateTime.now()
            ));
        }
    }
    
    /**
     * 系统信息
     */
    @GetMapping("/info")
    public ResponseEntity<Map<String, Object>> getSystemInfo() {
        Map<String, Object> info = new HashMap<>();
        
        // 应用信息
        info.put("application", Map.of(
                "name", "Sentiment Monitor",
                "version", "1.0.0",
                "description", "基于MediaCrawler的舆情监控系统"
        ));
        
        // 运行时信息
        Runtime runtime = Runtime.getRuntime();
        info.put("runtime", Map.of(
                "java_version", System.getProperty("java.version"),
                "max_memory", runtime.maxMemory() / 1024 / 1024 + " MB",
                "total_memory", runtime.totalMemory() / 1024 / 1024 + " MB",
                "free_memory", runtime.freeMemory() / 1024 / 1024 + " MB",
                "processors", runtime.availableProcessors()
        ));
        
        // 支持的平台
        info.put("supported_platforms", java.util.List.of(
                "xiaohongshu", "douyin", "weibo", "bilibili"
        ));
        
        // 功能特性
        info.put("features", java.util.List.of(
                "实时数据采集",
                "情感分析",
                "关键词监控",
                "预警系统",
                "统计报告",
                "API接口"
        ));
        
        info.put("timestamp", LocalDateTime.now());
        
        return ResponseEntity.ok(info);
    }
    
    /**
     * 获取最近的系统活动
     */
    @GetMapping("/activity")
    public ResponseEntity<Map<String, Object>> getRecentActivity() {
        Map<String, Object> activity = new HashMap<>();
        
        try {
            // 最近1小时的数据采集
            LocalDateTime oneHourAgo = LocalDateTime.now().minusHours(1);
            var recentData = sentimentDataRepository
                    .findByCrawlTimeBetween(oneHourAgo, LocalDateTime.now());
            
            // 按平台分组
            Map<String, Long> platformActivity = recentData.stream()
                    .collect(java.util.stream.Collectors.groupingBy(
                            data -> data.getPlatform(),
                            java.util.stream.Collectors.counting()
                    ));
            
            activity.put("data_collection_1h", Map.of(
                    "total", recentData.size(),
                    "by_platform", platformActivity
            ));
            
            // 最近触发的预警
            var recentAlerts = alertRuleRepository.findAll().stream()
                    .filter(rule -> rule.getLastTriggeredTime() != null)
                    .filter(rule -> rule.getLastTriggeredTime().isAfter(oneHourAgo))
                    .map(rule -> Map.of(
                            "rule_name", rule.getName(),
                            "triggered_time", rule.getLastTriggeredTime(),
                            "trigger_count", rule.getTriggerCount()
                    ))
                    .toList();
            
            activity.put("recent_alerts_1h", recentAlerts);
            
            // 活跃的监控配置
            var activeConfigs = monitorConfigRepository.findByIsEnabledTrue().stream()
                    .filter(config -> config.getLastExecutionTime() != null)
                    .filter(config -> config.getLastExecutionTime().isAfter(oneHourAgo))
                    .map(config -> Map.of(
                            "config_name", config.getName(),
                            "platform", config.getPlatform(),
                            "last_execution", config.getLastExecutionTime(),
                            "next_execution", config.getNextExecutionTime()
                    ))
                    .toList();
            
            activity.put("active_configs_1h", activeConfigs);
            
            activity.put("timestamp", LocalDateTime.now());
            
            return ResponseEntity.ok(activity);
            
        } catch (Exception e) {
            log.error("获取系统活动失败：{}", e.getMessage(), e);
            return ResponseEntity.status(500).body(Map.of(
                    "error", e.getMessage(),
                    "timestamp", LocalDateTime.now()
            ));
        }
    }
}
