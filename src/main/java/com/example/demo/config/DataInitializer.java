package com.example.demo.config;

import com.example.demo.entity.AlertRule;
import com.example.demo.entity.MonitorConfig;
import com.example.demo.repository.AlertRuleRepository;
import com.example.demo.repository.MonitorConfigRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 数据初始化器
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class DataInitializer implements CommandLineRunner {
    
    private final MonitorConfigRepository monitorConfigRepository;
    private final AlertRuleRepository alertRuleRepository;
    
    @Override
    public void run(String... args) throws Exception {
        log.info("开始初始化系统数据...");
        
        initializeMonitorConfigs();
        initializeAlertRules();
        
        log.info("系统数据初始化完成");
    }
    
    /**
     * 初始化监控配置
     */
    private void initializeMonitorConfigs() {
        if (monitorConfigRepository.count() > 0) {
            log.info("监控配置已存在，跳过初始化");
            return;
        }
        
        LocalDateTime now = LocalDateTime.now();
        
        // 小红书监控配置
        MonitorConfig xiaohongshuConfig = MonitorConfig.builder()
                .name("小红书品牌监控")
                .platform("xiaohongshu")
                .keywords("[\"品牌名称\", \"产品名称\"]")
                .intervalSeconds(300) // 5分钟
                .isEnabled(true)
                .createdTime(now)
                .updatedTime(now)
                .nextExecutionTime(now.plusSeconds(300))
                .extraConfig("{\"limit\": 50}")
                .build();
        
        // 抖音监控配置
        MonitorConfig douyinConfig = MonitorConfig.builder()
                .name("抖音品牌监控")
                .platform("douyin")
                .keywords("[\"品牌名称\", \"产品名称\"]")
                .intervalSeconds(600) // 10分钟
                .isEnabled(true)
                .createdTime(now)
                .updatedTime(now)
                .nextExecutionTime(now.plusSeconds(600))
                .extraConfig("{\"limit\": 50}")
                .build();
        
        // 微博监控配置
        MonitorConfig weiboConfig = MonitorConfig.builder()
                .name("微博品牌监控")
                .platform("weibo")
                .keywords("[\"品牌名称\", \"产品名称\"]")
                .intervalSeconds(300) // 5分钟
                .isEnabled(true)
                .createdTime(now)
                .updatedTime(now)
                .nextExecutionTime(now.plusSeconds(300))
                .extraConfig("{\"limit\": 50}")
                .build();
        
        monitorConfigRepository.save(xiaohongshuConfig);
        monitorConfigRepository.save(douyinConfig);
        monitorConfigRepository.save(weiboConfig);
        
        log.info("初始化了3个监控配置");
    }
    
    /**
     * 初始化预警规则
     */
    private void initializeAlertRules() {
        if (alertRuleRepository.count() > 0) {
            log.info("预警规则已存在，跳过初始化");
            return;
        }
        
        LocalDateTime now = LocalDateTime.now();
        
        // 负面情感预警规则
        AlertRule sentimentRule = AlertRule.builder()
                .name("负面情感预警")
                .description("检测到大量负面情感时触发预警")
                .ruleType("sentiment")
                .platforms("xiaohongshu,douyin,weibo")
                .keywords("[\"品牌名称\", \"产品名称\"]")
                .thresholdConfig("{\"negative_threshold\": 0.7, \"min_count\": 5}")
                .timeWindowMinutes(60) // 1小时时间窗口
                .isEnabled(true)
                .notificationMethods("[\"email\", \"webhook\"]")
                .notificationConfig("{\"email\": \"<EMAIL>\", \"webhook_url\": \"http://localhost:8080/webhook\"}")
                .triggerCount(0)
                .createdTime(now)
                .updatedTime(now)
                .build();
        
        // 关键词提及量预警规则
        AlertRule keywordRule = AlertRule.builder()
                .name("关键词提及量预警")
                .description("关键词提及量异常时触发预警")
                .ruleType("keyword")
                .platforms("xiaohongshu,douyin,weibo")
                .keywords("[\"品牌名称\", \"产品名称\"]")
                .thresholdConfig("{\"mention_threshold\": 100}")
                .timeWindowMinutes(60) // 1小时时间窗口
                .isEnabled(true)
                .notificationMethods("[\"email\"]")
                .notificationConfig("{\"email\": \"<EMAIL>\"}")
                .triggerCount(0)
                .createdTime(now)
                .updatedTime(now)
                .build();
        
        // 内容数量预警规则
        AlertRule volumeRule = AlertRule.builder()
                .name("内容数量预警")
                .description("内容数量异常时触发预警")
                .ruleType("volume")
                .platforms("xiaohongshu,douyin,weibo")
                .keywords("[\"品牌名称\", \"产品名称\"]")
                .thresholdConfig("{\"volume_threshold\": 1000}")
                .timeWindowMinutes(60) // 1小时时间窗口
                .isEnabled(false) // 默认禁用
                .notificationMethods("[\"email\"]")
                .notificationConfig("{\"email\": \"<EMAIL>\"}")
                .triggerCount(0)
                .createdTime(now)
                .updatedTime(now)
                .build();
        
        alertRuleRepository.save(sentimentRule);
        alertRuleRepository.save(keywordRule);
        alertRuleRepository.save(volumeRule);
        
        log.info("初始化了3个预警规则");
    }
}
