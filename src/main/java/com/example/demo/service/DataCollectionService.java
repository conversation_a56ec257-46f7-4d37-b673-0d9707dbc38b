package com.example.demo.service;

import com.example.demo.dto.MediaCrawlerResponse;
import com.example.demo.dto.SentimentAnalysisResult;
import com.example.demo.entity.MonitorConfig;
import com.example.demo.entity.SentimentData;
import com.example.demo.repository.MonitorConfigRepository;
import com.example.demo.repository.SentimentDataRepository;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * 数据采集服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DataCollectionService {
    
    private final MediaCrawlerClient mediaCrawlerClient;
    private final SentimentAnalysisService sentimentAnalysisService;
    private final SentimentDataRepository sentimentDataRepository;
    private final MonitorConfigRepository monitorConfigRepository;
    private final ObjectMapper objectMapper;
    
    /**
     * 根据监控配置采集数据
     */
    @Transactional
    public void collectDataByConfig(MonitorConfig config) {
        log.info("开始根据配置采集数据：{}", config.getName());
        
        try {
            List<String> keywords = parseKeywords(config.getKeywords());
            if (keywords.isEmpty()) {
                log.warn("配置{}没有关键词，跳过采集", config.getName());
                return;
            }
            
            MediaCrawlerResponse response = null;
            
            // 根据平台调用不同的采集方法
            switch (config.getPlatform().toLowerCase()) {
                case "xiaohongshu":
                    response = mediaCrawlerClient.getXiaohongshuData(keywords, 50).block();
                    break;
                case "douyin":
                    response = mediaCrawlerClient.getDouyinData(keywords, 50).block();
                    break;
                case "weibo":
                    response = mediaCrawlerClient.getWeiboData(keywords, 50).block();
                    break;
                case "bilibili":
                    response = mediaCrawlerClient.getBilibiliData(keywords, 50).block();
                    break;
                default:
                    log.warn("不支持的平台：{}", config.getPlatform());
                    return;
            }
            
            if (response != null && response.getData() != null) {
                int savedCount = 0;
                for (MediaCrawlerResponse.ContentData contentData : response.getData()) {
                    if (saveContentData(contentData, config.getPlatform(), keywords)) {
                        savedCount++;
                    }
                }
                log.info("配置{}采集完成，保存{}条新数据", config.getName(), savedCount);
            } else {
                log.warn("配置{}采集失败或无数据", config.getName());
            }
            
            // 更新配置的执行时间
            updateConfigExecutionTime(config);
            
        } catch (Exception e) {
            log.error("配置{}采集数据时发生错误：{}", config.getName(), e.getMessage(), e);
        }
    }
    
    /**
     * 保存内容数据
     */
    private boolean saveContentData(MediaCrawlerResponse.ContentData contentData, String platform, List<String> keywords) {
        try {
            // 检查是否已存在
            Optional<SentimentData> existing = sentimentDataRepository
                    .findByPlatformAndContentId(platform, contentData.getId());
            
            if (existing.isPresent()) {
                log.debug("内容{}已存在，跳过保存", contentData.getId());
                return false;
            }
            
            // 进行情感分析
            String textToAnalyze = (contentData.getTitle() != null ? contentData.getTitle() + " " : "") +
                                   (contentData.getContent() != null ? contentData.getContent() : "");
            
            SentimentAnalysisResult sentimentResult = sentimentAnalysisService.analyzeSentiment(textToAnalyze);
            
            // 解析发布时间
            LocalDateTime publishTime = parsePublishTime(contentData.getPublishTime());
            
            // 构建SentimentData
            SentimentData sentimentData = SentimentData.builder()
                    .platform(platform)
                    .contentId(contentData.getId())
                    .contentType(contentData.getType())
                    .title(contentData.getTitle())
                    .content(contentData.getContent())
                    .author(contentData.getAuthor())
                    .publishTime(publishTime)
                    .crawlTime(LocalDateTime.now())
                    .sentiment(sentimentResult.getSentiment())
                    .sentimentScore(sentimentResult.getConfidence())
                    .likeCount(contentData.getLikeCount())
                    .commentCount(contentData.getCommentCount())
                    .shareCount(contentData.getShareCount())
                    .viewCount(contentData.getViewCount())
                    .matchedKeywords(String.join(",", keywords))
                    .sourceUrl(contentData.getUrl())
                    .isProcessed(true)
                    .isAlerted(false)
                    .extraData(serializeExtraData(contentData))
                    .build();
            
            sentimentDataRepository.save(sentimentData);
            log.debug("保存内容数据：{}", contentData.getId());
            return true;
            
        } catch (Exception e) {
            log.error("保存内容数据时发生错误：{}", e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 解析关键词
     */
    private List<String> parseKeywords(String keywordsJson) {
        try {
            if (keywordsJson == null || keywordsJson.trim().isEmpty()) {
                return List.of();
            }
            
            // 尝试解析JSON数组
            if (keywordsJson.trim().startsWith("[")) {
                return objectMapper.readValue(keywordsJson, new TypeReference<List<String>>() {});
            } else {
                // 如果不是JSON，按逗号分割
                return Arrays.asList(keywordsJson.split(","))
                        .stream()
                        .map(String::trim)
                        .filter(s -> !s.isEmpty())
                        .toList();
            }
        } catch (JsonProcessingException e) {
            log.warn("解析关键词失败：{}，使用逗号分割", keywordsJson);
            return Arrays.asList(keywordsJson.split(","))
                    .stream()
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .toList();
        }
    }
    
    /**
     * 解析发布时间
     */
    private LocalDateTime parsePublishTime(String publishTimeStr) {
        if (publishTimeStr == null || publishTimeStr.trim().isEmpty()) {
            return LocalDateTime.now();
        }
        
        // 尝试多种时间格式
        String[] patterns = {
                "yyyy-MM-dd HH:mm:ss",
                "yyyy-MM-dd'T'HH:mm:ss",
                "yyyy-MM-dd'T'HH:mm:ss.SSS",
                "yyyy-MM-dd'T'HH:mm:ss'Z'",
                "yyyy-MM-dd HH:mm",
                "yyyy-MM-dd"
        };
        
        for (String pattern : patterns) {
            try {
                return LocalDateTime.parse(publishTimeStr, DateTimeFormatter.ofPattern(pattern));
            } catch (DateTimeParseException e) {
                // 继续尝试下一个格式
            }
        }
        
        log.warn("无法解析发布时间：{}，使用当前时间", publishTimeStr);
        return LocalDateTime.now();
    }
    
    /**
     * 序列化额外数据
     */
    private String serializeExtraData(MediaCrawlerResponse.ContentData contentData) {
        try {
            return objectMapper.writeValueAsString(contentData.getExtraData());
        } catch (JsonProcessingException e) {
            log.warn("序列化额外数据失败：{}", e.getMessage());
            return "{}";
        }
    }
    
    /**
     * 更新配置执行时间
     */
    private void updateConfigExecutionTime(MonitorConfig config) {
        LocalDateTime now = LocalDateTime.now();
        config.setLastExecutionTime(now);
        config.setNextExecutionTime(now.plusSeconds(config.getIntervalSeconds()));
        config.setUpdatedTime(now);
        monitorConfigRepository.save(config);
    }
    
    /**
     * 手动采集指定平台数据
     */
    public void collectDataManually(String platform, List<String> keywords, int limit) {
        log.info("手动采集{}数据，关键词：{}，限制：{}", platform, keywords, limit);
        
        MediaCrawlerResponse response = null;
        
        switch (platform.toLowerCase()) {
            case "xiaohongshu":
                response = mediaCrawlerClient.getXiaohongshuData(keywords, limit).block();
                break;
            case "douyin":
                response = mediaCrawlerClient.getDouyinData(keywords, limit).block();
                break;
            case "weibo":
                response = mediaCrawlerClient.getWeiboData(keywords, limit).block();
                break;
            case "bilibili":
                response = mediaCrawlerClient.getBilibiliData(keywords, limit).block();
                break;
            default:
                throw new IllegalArgumentException("不支持的平台：" + platform);
        }
        
        if (response != null && response.getData() != null) {
            int savedCount = 0;
            for (MediaCrawlerResponse.ContentData contentData : response.getData()) {
                if (saveContentData(contentData, platform, keywords)) {
                    savedCount++;
                }
            }
            log.info("手动采集完成，保存{}条新数据", savedCount);
        }
    }
}
