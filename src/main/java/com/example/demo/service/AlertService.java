package com.example.demo.service;

import com.example.demo.entity.AlertRule;
import com.example.demo.entity.SentimentData;
import com.example.demo.repository.AlertRuleRepository;
import com.example.demo.repository.SentimentDataRepository;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 预警服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AlertService {
    
    private final AlertRuleRepository alertRuleRepository;
    private final SentimentDataRepository sentimentDataRepository;
    private final ObjectMapper objectMapper;
    
    @Value("${sentiment.monitor.alert.enabled:true}")
    private boolean alertEnabled;
    
    /**
     * 检查所有预警规则
     */
    @Transactional
    public void checkAllAlertRules() {
        if (!alertEnabled) {
            log.debug("预警功能已禁用");
            return;
        }
        
        List<AlertRule> enabledRules = alertRuleRepository.findByIsEnabledTrue();
        log.info("开始检查{}条预警规则", enabledRules.size());
        
        for (AlertRule rule : enabledRules) {
            try {
                checkAlertRule(rule);
            } catch (Exception e) {
                log.error("检查预警规则{}时发生错误：{}", rule.getName(), e.getMessage(), e);
            }
        }
    }
    
    /**
     * 检查单个预警规则
     */
    private void checkAlertRule(AlertRule rule) {
        log.debug("检查预警规则：{}", rule.getName());
        
        switch (rule.getRuleType().toLowerCase()) {
            case "sentiment":
                checkSentimentRule(rule);
                break;
            case "keyword":
                checkKeywordRule(rule);
                break;
            case "volume":
                checkVolumeRule(rule);
                break;
            default:
                log.warn("未知的预警规则类型：{}", rule.getRuleType());
        }
    }
    
    /**
     * 检查情感预警规则
     */
    private void checkSentimentRule(AlertRule rule) {
        try {
            Map<String, Object> thresholdConfig = parseThresholdConfig(rule.getThresholdConfig());
            double negativeThreshold = getDoubleValue(thresholdConfig, "negative_threshold", 0.7);
            int minCount = getIntValue(thresholdConfig, "min_count", 5);
            
            LocalDateTime timeWindow = LocalDateTime.now().minusMinutes(rule.getTimeWindowMinutes());
            
            // 查找时间窗口内的负面情感数据
            List<SentimentData> negativeData = sentimentDataRepository
                    .findBySentimentAndIsAlertedFalse("negative")
                    .stream()
                    .filter(data -> data.getCrawlTime().isAfter(timeWindow))
                    .filter(data -> matchesPlatforms(data.getPlatform(), rule.getPlatforms()))
                    .filter(data -> matchesKeywords(data, rule.getKeywords()))
                    .filter(data -> data.getSentimentScore() != null && data.getSentimentScore() >= negativeThreshold)
                    .toList();
            
            if (negativeData.size() >= minCount) {
                triggerAlert(rule, "检测到大量负面情感", 
                        String.format("在%d分钟内检测到%d条负面情感内容，超过阈值%d", 
                                rule.getTimeWindowMinutes(), negativeData.size(), minCount));
                
                // 标记为已预警
                negativeData.forEach(data -> {
                    data.setIsAlerted(true);
                    sentimentDataRepository.save(data);
                });
            }
            
        } catch (Exception e) {
            log.error("检查情感预警规则{}时发生错误：{}", rule.getName(), e.getMessage(), e);
        }
    }
    
    /**
     * 检查关键词预警规则
     */
    private void checkKeywordRule(AlertRule rule) {
        try {
            Map<String, Object> thresholdConfig = parseThresholdConfig(rule.getThresholdConfig());
            int mentionThreshold = getIntValue(thresholdConfig, "mention_threshold", 100);
            
            LocalDateTime timeWindow = LocalDateTime.now().minusMinutes(rule.getTimeWindowMinutes());
            List<String> keywords = parseKeywords(rule.getKeywords());
            
            for (String keyword : keywords) {
                List<SentimentData> mentionData = sentimentDataRepository
                        .findByKeyword(keyword)
                        .stream()
                        .filter(data -> data.getCrawlTime().isAfter(timeWindow))
                        .filter(data -> matchesPlatforms(data.getPlatform(), rule.getPlatforms()))
                        .toList();
                
                if (mentionData.size() >= mentionThreshold) {
                    triggerAlert(rule, "关键词提及量异常", 
                            String.format("关键词'%s'在%d分钟内被提及%d次，超过阈值%d", 
                                    keyword, rule.getTimeWindowMinutes(), mentionData.size(), mentionThreshold));
                }
            }
            
        } catch (Exception e) {
            log.error("检查关键词预警规则{}时发生错误：{}", rule.getName(), e.getMessage(), e);
        }
    }
    
    /**
     * 检查数量预警规则
     */
    private void checkVolumeRule(AlertRule rule) {
        try {
            Map<String, Object> thresholdConfig = parseThresholdConfig(rule.getThresholdConfig());
            int volumeThreshold = getIntValue(thresholdConfig, "volume_threshold", 1000);
            
            LocalDateTime timeWindow = LocalDateTime.now().minusMinutes(rule.getTimeWindowMinutes());
            
            List<SentimentData> recentData = sentimentDataRepository
                    .findByCrawlTimeBetween(timeWindow, LocalDateTime.now())
                    .stream()
                    .filter(data -> matchesPlatforms(data.getPlatform(), rule.getPlatforms()))
                    .filter(data -> matchesKeywords(data, rule.getKeywords()))
                    .toList();
            
            if (recentData.size() >= volumeThreshold) {
                triggerAlert(rule, "内容数量异常", 
                        String.format("在%d分钟内检测到%d条内容，超过阈值%d", 
                                rule.getTimeWindowMinutes(), recentData.size(), volumeThreshold));
            }
            
        } catch (Exception e) {
            log.error("检查数量预警规则{}时发生错误：{}", rule.getName(), e.getMessage(), e);
        }
    }
    
    /**
     * 触发预警
     */
    private void triggerAlert(AlertRule rule, String alertType, String message) {
        log.warn("触发预警 - 规则：{}，类型：{}，消息：{}", rule.getName(), alertType, message);
        
        // 更新规则触发信息
        rule.setLastTriggeredTime(LocalDateTime.now());
        rule.setTriggerCount(rule.getTriggerCount() + 1);
        alertRuleRepository.save(rule);
        
        // 发送通知
        sendNotification(rule, alertType, message);
    }
    
    /**
     * 发送通知
     */
    private void sendNotification(AlertRule rule, String alertType, String message) {
        try {
            List<String> notificationMethods = parseNotificationMethods(rule.getNotificationMethods());
            
            for (String method : notificationMethods) {
                switch (method.toLowerCase()) {
                    case "email":
                        sendEmailNotification(rule, alertType, message);
                        break;
                    case "webhook":
                        sendWebhookNotification(rule, alertType, message);
                        break;
                    case "sms":
                        sendSmsNotification(rule, alertType, message);
                        break;
                    default:
                        log.warn("未知的通知方式：{}", method);
                }
            }
        } catch (Exception e) {
            log.error("发送通知时发生错误：{}", e.getMessage(), e);
        }
    }
    
    /**
     * 发送邮件通知 (占位符实现)
     */
    private void sendEmailNotification(AlertRule rule, String alertType, String message) {
        // TODO: 实现邮件通知
        log.info("邮件通知 - 规则：{}，类型：{}，消息：{}", rule.getName(), alertType, message);
    }
    
    /**
     * 发送Webhook通知 (占位符实现)
     */
    private void sendWebhookNotification(AlertRule rule, String alertType, String message) {
        // TODO: 实现Webhook通知
        log.info("Webhook通知 - 规则：{}，类型：{}，消息：{}", rule.getName(), alertType, message);
    }
    
    /**
     * 发送短信通知 (占位符实现)
     */
    private void sendSmsNotification(AlertRule rule, String alertType, String message) {
        // TODO: 实现短信通知
        log.info("短信通知 - 规则：{}，类型：{}，消息：{}", rule.getName(), alertType, message);
    }
    
    // 辅助方法
    private Map<String, Object> parseThresholdConfig(String configJson) {
        try {
            if (configJson == null || configJson.trim().isEmpty()) {
                return Map.of();
            }
            return objectMapper.readValue(configJson, new TypeReference<Map<String, Object>>() {});
        } catch (JsonProcessingException e) {
            log.warn("解析阈值配置失败：{}", configJson);
            return Map.of();
        }
    }
    
    private List<String> parseKeywords(String keywordsJson) {
        try {
            if (keywordsJson == null || keywordsJson.trim().isEmpty()) {
                return List.of();
            }
            return objectMapper.readValue(keywordsJson, new TypeReference<List<String>>() {});
        } catch (JsonProcessingException e) {
            return Arrays.asList(keywordsJson.split(","))
                    .stream()
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .toList();
        }
    }
    
    private List<String> parseNotificationMethods(String methodsJson) {
        try {
            if (methodsJson == null || methodsJson.trim().isEmpty()) {
                return List.of("email");
            }
            return objectMapper.readValue(methodsJson, new TypeReference<List<String>>() {});
        } catch (JsonProcessingException e) {
            return Arrays.asList(methodsJson.split(","))
                    .stream()
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .toList();
        }
    }
    
    private boolean matchesPlatforms(String platform, String platformsConfig) {
        if (platformsConfig == null || platformsConfig.trim().isEmpty()) {
            return true; // 空配置表示匹配所有平台
        }
        return Arrays.asList(platformsConfig.split(","))
                .stream()
                .map(String::trim)
                .anyMatch(p -> p.equalsIgnoreCase(platform));
    }
    
    private boolean matchesKeywords(SentimentData data, String keywordsConfig) {
        if (keywordsConfig == null || keywordsConfig.trim().isEmpty()) {
            return true; // 空配置表示匹配所有关键词
        }
        
        List<String> keywords = parseKeywords(keywordsConfig);
        String content = (data.getTitle() != null ? data.getTitle() + " " : "") +
                        (data.getContent() != null ? data.getContent() : "");
        
        return keywords.stream()
                .anyMatch(keyword -> content.toLowerCase().contains(keyword.toLowerCase()));
    }
    
    private double getDoubleValue(Map<String, Object> map, String key, double defaultValue) {
        Object value = map.get(key);
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        return defaultValue;
    }
    
    private int getIntValue(Map<String, Object> map, String key, int defaultValue) {
        Object value = map.get(key);
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        return defaultValue;
    }
}
