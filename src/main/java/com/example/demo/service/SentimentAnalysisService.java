package com.example.demo.service;

import com.example.demo.dto.SentimentAnalysisResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 情感分析服务
 */
@Service
@Slf4j
public class SentimentAnalysisService {
    
    @Value("${sentiment.monitor.sentiment-analysis.enabled:true}")
    private boolean enabled;
    
    @Value("${sentiment.monitor.sentiment-analysis.provider:local}")
    private String provider;
    
    @Value("${sentiment.monitor.sentiment-analysis.confidence-threshold:0.6}")
    private double confidenceThreshold;
    
    // 简单的情感词典 (实际项目中应该使用更完整的词典)
    private static final List<String> POSITIVE_WORDS = Arrays.asList(
            "好", "棒", "赞", "优秀", "完美", "喜欢", "爱", "满意", "推荐", "值得",
            "amazing", "good", "great", "excellent", "perfect", "love", "like", "recommend"
    );
    
    private static final List<String> NEGATIVE_WORDS = Arrays.asList(
            "差", "烂", "垃圾", "失望", "糟糕", "讨厌", "恨", "不满", "投诉", "退货",
            "bad", "terrible", "awful", "hate", "disappointed", "worst", "complain"
    );
    
    private static final Pattern EMOJI_POSITIVE = Pattern.compile("[😀😃😄😁😆😊😍🥰😘🤗👍❤️💕]");
    private static final Pattern EMOJI_NEGATIVE = Pattern.compile("[😞😢😭😠😡🤬👎💔😤]");
    
    /**
     * 分析文本情感
     */
    public SentimentAnalysisResult analyzeSentiment(String text) {
        if (!enabled || text == null || text.trim().isEmpty()) {
            return SentimentAnalysisResult.builder()
                    .sentiment("neutral")
                    .confidence(0.0)
                    .provider("none")
                    .timestamp(System.currentTimeMillis())
                    .build();
        }
        
        log.debug("开始分析文本情感：{}", text.substring(0, Math.min(text.length(), 50)));
        
        switch (provider.toLowerCase()) {
            case "local":
                return analyzeWithLocalDictionary(text);
            case "baidu":
                return analyzeWithBaiduAPI(text);
            case "tencent":
                return analyzeWithTencentAPI(text);
            default:
                log.warn("未知的情感分析提供者：{}，使用本地分析", provider);
                return analyzeWithLocalDictionary(text);
        }
    }
    
    /**
     * 使用本地词典进行情感分析
     */
    private SentimentAnalysisResult analyzeWithLocalDictionary(String text) {
        String lowerText = text.toLowerCase();
        
        // 计算正面词汇数量
        long positiveCount = POSITIVE_WORDS.stream()
                .mapToLong(word -> countOccurrences(lowerText, word))
                .sum();
        
        // 计算负面词汇数量
        long negativeCount = NEGATIVE_WORDS.stream()
                .mapToLong(word -> countOccurrences(lowerText, word))
                .sum();
        
        // 计算表情符号
        long positiveEmojiCount = EMOJI_POSITIVE.matcher(text).results().count();
        long negativeEmojiCount = EMOJI_NEGATIVE.matcher(text).results().count();
        
        positiveCount += positiveEmojiCount;
        negativeCount += negativeEmojiCount;
        
        // 计算情感得分
        double totalWords = positiveCount + negativeCount;
        double positiveScore = totalWords > 0 ? positiveCount / totalWords : 0.0;
        double negativeScore = totalWords > 0 ? negativeCount / totalWords : 0.0;
        double neutralScore = 1.0 - positiveScore - negativeScore;
        
        // 确定主要情感
        String sentiment;
        double confidence;
        
        if (positiveScore > negativeScore && positiveScore > 0.3) {
            sentiment = "positive";
            confidence = positiveScore;
        } else if (negativeScore > positiveScore && negativeScore > 0.3) {
            sentiment = "negative";
            confidence = negativeScore;
        } else {
            sentiment = "neutral";
            confidence = neutralScore;
        }
        
        // 调整置信度
        if (totalWords == 0) {
            confidence = 0.5; // 没有情感词汇时，中等置信度的中性
        } else if (totalWords < 3) {
            confidence *= 0.7; // 情感词汇较少时，降低置信度
        }
        
        log.debug("本地情感分析结果：情感={}, 置信度={}, 正面词={}, 负面词={}", 
                sentiment, confidence, positiveCount, negativeCount);
        
        return SentimentAnalysisResult.builder()
                .sentiment(sentiment)
                .confidence(confidence)
                .positiveScore(positiveScore)
                .negativeScore(negativeScore)
                .neutralScore(neutralScore)
                .provider("local")
                .timestamp(System.currentTimeMillis())
                .extraInfo(String.format("positive_words=%d, negative_words=%d", positiveCount, negativeCount))
                .build();
    }
    
    /**
     * 使用百度API进行情感分析 (占位符实现)
     */
    private SentimentAnalysisResult analyzeWithBaiduAPI(String text) {
        // TODO: 实现百度情感分析API调用
        log.warn("百度API情感分析尚未实现，使用本地分析");
        return analyzeWithLocalDictionary(text);
    }
    
    /**
     * 使用腾讯API进行情感分析 (占位符实现)
     */
    private SentimentAnalysisResult analyzeWithTencentAPI(String text) {
        // TODO: 实现腾讯情感分析API调用
        log.warn("腾讯API情感分析尚未实现，使用本地分析");
        return analyzeWithLocalDictionary(text);
    }
    
    /**
     * 计算字符串中某个词出现的次数
     */
    private long countOccurrences(String text, String word) {
        int count = 0;
        int index = 0;
        while ((index = text.indexOf(word, index)) != -1) {
            count++;
            index += word.length();
        }
        return count;
    }
    
    /**
     * 批量分析情感
     */
    public List<SentimentAnalysisResult> batchAnalyzeSentiment(List<String> texts) {
        return texts.stream()
                .map(this::analyzeSentiment)
                .toList();
    }
}
