package com.example.demo.service;

import com.example.demo.entity.MonitorConfig;
import com.example.demo.repository.MonitorConfigRepository;
import com.example.demo.repository.SentimentDataRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 定时任务服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ScheduledTaskService {
    
    private final DataCollectionService dataCollectionService;
    private final AlertService alertService;
    private final MonitorConfigRepository monitorConfigRepository;
    private final SentimentDataRepository sentimentDataRepository;
    
    /**
     * 数据采集任务 - 每分钟执行一次
     */
    @Scheduled(fixedRate = 60000) // 60秒
    public void dataCollectionTask() {
        try {
            log.debug("开始执行数据采集任务");
            
            // 查找需要执行的监控配置
            LocalDateTime now = LocalDateTime.now();
            List<MonitorConfig> pendingConfigs = monitorConfigRepository
                    .findByIsEnabledTrueAndNextExecutionTimeBefore(now);
            
            if (pendingConfigs.isEmpty()) {
                log.debug("没有需要执行的监控配置");
                return;
            }
            
            log.info("找到{}个需要执行的监控配置", pendingConfigs.size());
            
            for (MonitorConfig config : pendingConfigs) {
                try {
                    dataCollectionService.collectDataByConfig(config);
                } catch (Exception e) {
                    log.error("执行监控配置{}时发生错误：{}", config.getName(), e.getMessage(), e);
                }
            }
            
        } catch (Exception e) {
            log.error("数据采集任务执行失败：{}", e.getMessage(), e);
        }
    }
    
    /**
     * 预警检查任务 - 每5分钟执行一次
     */
    @Scheduled(fixedRate = 300000) // 5分钟
    public void alertCheckTask() {
        try {
            log.debug("开始执行预警检查任务");
            alertService.checkAllAlertRules();
        } catch (Exception e) {
            log.error("预警检查任务执行失败：{}", e.getMessage(), e);
        }
    }
    
    /**
     * 数据清理任务 - 每天凌晨2点执行
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void dataCleanupTask() {
        try {
            log.info("开始执行数据清理任务");
            
            // 删除30天前的数据
            LocalDateTime cutoffTime = LocalDateTime.now().minusDays(30);
            sentimentDataRepository.deleteByCrawlTimeBefore(cutoffTime);
            
            log.info("数据清理任务完成，删除了{}之前的数据", cutoffTime);
            
        } catch (Exception e) {
            log.error("数据清理任务执行失败：{}", e.getMessage(), e);
        }
    }
    
    /**
     * 系统健康检查任务 - 每小时执行一次
     */
    @Scheduled(fixedRate = 3600000) // 1小时
    public void healthCheckTask() {
        try {
            log.debug("开始执行系统健康检查");
            
            // 检查数据库连接
            long totalRecords = sentimentDataRepository.count();
            log.info("当前数据库中共有{}条舆情数据", totalRecords);
            
            // 检查最近1小时的数据采集情况
            LocalDateTime oneHourAgo = LocalDateTime.now().minusHours(1);
            long recentRecords = sentimentDataRepository
                    .findByCrawlTimeBetween(oneHourAgo, LocalDateTime.now())
                    .size();
            
            if (recentRecords == 0) {
                log.warn("最近1小时没有采集到新数据，请检查采集服务");
            } else {
                log.info("最近1小时采集了{}条新数据", recentRecords);
            }
            
            // 检查启用的监控配置数量
            long enabledConfigs = monitorConfigRepository.findByIsEnabledTrue().size();
            log.info("当前有{}个启用的监控配置", enabledConfigs);
            
        } catch (Exception e) {
            log.error("系统健康检查失败：{}", e.getMessage(), e);
        }
    }
    
    /**
     * 统计报告任务 - 每天早上8点执行
     */
    @Scheduled(cron = "0 0 8 * * ?")
    public void dailyReportTask() {
        try {
            log.info("开始生成每日统计报告");
            
            LocalDateTime yesterday = LocalDateTime.now().minusDays(1);
            LocalDateTime today = LocalDateTime.now();
            
            // 统计昨天的数据
            List<SentimentData> yesterdayData = sentimentDataRepository
                    .findByCrawlTimeBetween(yesterday, today);
            
            if (yesterdayData.isEmpty()) {
                log.info("昨天没有采集到数据");
                return;
            }
            
            // 按情感分类统计
            long positiveCount = yesterdayData.stream()
                    .filter(data -> "positive".equals(data.getSentiment()))
                    .count();
            
            long negativeCount = yesterdayData.stream()
                    .filter(data -> "negative".equals(data.getSentiment()))
                    .count();
            
            long neutralCount = yesterdayData.stream()
                    .filter(data -> "neutral".equals(data.getSentiment()))
                    .count();
            
            // 按平台统计
            long xiaohongshuCount = yesterdayData.stream()
                    .filter(data -> "xiaohongshu".equals(data.getPlatform()))
                    .count();
            
            long douyinCount = yesterdayData.stream()
                    .filter(data -> "douyin".equals(data.getPlatform()))
                    .count();
            
            long weiboCount = yesterdayData.stream()
                    .filter(data -> "weibo".equals(data.getPlatform()))
                    .count();
            
            long bilibiliCount = yesterdayData.stream()
                    .filter(data -> "bilibili".equals(data.getPlatform()))
                    .count();
            
            // 生成报告
            StringBuilder report = new StringBuilder();
            report.append("=== 每日舆情监控报告 ===\n");
            report.append("日期：").append(yesterday.toLocalDate()).append("\n");
            report.append("总数据量：").append(yesterdayData.size()).append("条\n\n");
            
            report.append("情感分布：\n");
            report.append("- 正面：").append(positiveCount).append("条 (")
                    .append(String.format("%.1f", positiveCount * 100.0 / yesterdayData.size())).append("%)\n");
            report.append("- 负面：").append(negativeCount).append("条 (")
                    .append(String.format("%.1f", negativeCount * 100.0 / yesterdayData.size())).append("%)\n");
            report.append("- 中性：").append(neutralCount).append("条 (")
                    .append(String.format("%.1f", neutralCount * 100.0 / yesterdayData.size())).append("%)\n\n");
            
            report.append("平台分布：\n");
            if (xiaohongshuCount > 0) {
                report.append("- 小红书：").append(xiaohongshuCount).append("条\n");
            }
            if (douyinCount > 0) {
                report.append("- 抖音：").append(douyinCount).append("条\n");
            }
            if (weiboCount > 0) {
                report.append("- 微博：").append(weiboCount).append("条\n");
            }
            if (bilibiliCount > 0) {
                report.append("- B站：").append(bilibiliCount).append("条\n");
            }
            
            log.info("每日统计报告：\n{}", report.toString());
            
        } catch (Exception e) {
            log.error("生成每日统计报告失败：{}", e.getMessage(), e);
        }
    }
}
