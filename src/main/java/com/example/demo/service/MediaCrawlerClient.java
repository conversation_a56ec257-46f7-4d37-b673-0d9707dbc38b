package com.example.demo.service;

import com.example.demo.dto.MediaCrawlerResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.util.List;
import java.util.Map;

/**
 * MediaCrawler客户端服务
 */
@Service
@Slf4j
public class MediaCrawlerClient {
    
    private final WebClient webClient;
    
    @Value("${sentiment.monitor.mediacrawler.base-url}")
    private String baseUrl;
    
    @Value("${sentiment.monitor.mediacrawler.timeout:30000}")
    private int timeout;
    
    @Value("${sentiment.monitor.mediacrawler.retry-count:3}")
    private int retryCount;
    
    public MediaCrawlerClient() {
        this.webClient = WebClient.builder()
                .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(10 * 1024 * 1024))
                .build();
    }
    
    /**
     * 获取小红书数据
     */
    public Mono<MediaCrawlerResponse> getXiaohongshuData(List<String> keywords, int limit) {
        return crawlData("xiaohongshu", keywords, limit);
    }
    
    /**
     * 获取抖音数据
     */
    public Mono<MediaCrawlerResponse> getDouyinData(List<String> keywords, int limit) {
        return crawlData("douyin", keywords, limit);
    }
    
    /**
     * 获取微博数据
     */
    public Mono<MediaCrawlerResponse> getWeiboData(List<String> keywords, int limit) {
        return crawlData("weibo", keywords, limit);
    }
    
    /**
     * 获取B站数据
     */
    public Mono<MediaCrawlerResponse> getBilibiliData(List<String> keywords, int limit) {
        return crawlData("bilibili", keywords, limit);
    }
    
    /**
     * 通用爬取方法
     */
    private Mono<MediaCrawlerResponse> crawlData(String platform, List<String> keywords, int limit) {
        log.info("开始爬取{}数据，关键词：{}，限制：{}", platform, keywords, limit);
        
        Map<String, Object> requestBody = Map.of(
                "platform", platform,
                "keywords", keywords,
                "limit", limit,
                "type", "search"
        );
        
        return webClient.post()
                .uri(baseUrl + "/api/crawl")
                .bodyValue(requestBody)
                .retrieve()
                .bodyToMono(MediaCrawlerResponse.class)
                .timeout(Duration.ofMillis(timeout))
                .retryWhen(Retry.fixedDelay(retryCount, Duration.ofSeconds(2)))
                .doOnSuccess(response -> {
                    if (response != null && response.getData() != null) {
                        log.info("成功获取{}数据，数量：{}", platform, response.getData().size());
                    }
                })
                .doOnError(error -> {
                    log.error("获取{}数据失败：{}", platform, error.getMessage());
                })
                .onErrorReturn(MediaCrawlerResponse.builder()
                        .status("error")
                        .message("Failed to crawl data from " + platform)
                        .build());
    }
    
    /**
     * 获取评论数据
     */
    public Mono<MediaCrawlerResponse> getComments(String platform, String contentId, int limit) {
        log.info("开始获取{}评论数据，内容ID：{}，限制：{}", platform, contentId, limit);
        
        Map<String, Object> requestBody = Map.of(
                "platform", platform,
                "content_id", contentId,
                "limit", limit,
                "type", "comments"
        );
        
        return webClient.post()
                .uri(baseUrl + "/api/comments")
                .bodyValue(requestBody)
                .retrieve()
                .bodyToMono(MediaCrawlerResponse.class)
                .timeout(Duration.ofMillis(timeout))
                .retryWhen(Retry.fixedDelay(retryCount, Duration.ofSeconds(2)))
                .doOnSuccess(response -> {
                    if (response != null && response.getData() != null) {
                        log.info("成功获取{}评论数据，数量：{}", platform, response.getData().size());
                    }
                })
                .doOnError(error -> {
                    log.error("获取{}评论数据失败：{}", platform, error.getMessage());
                })
                .onErrorReturn(MediaCrawlerResponse.builder()
                        .status("error")
                        .message("Failed to get comments from " + platform)
                        .build());
    }
    
    /**
     * 检查MediaCrawler服务状态
     */
    public Mono<Boolean> checkHealth() {
        return webClient.get()
                .uri(baseUrl + "/health")
                .retrieve()
                .bodyToMono(String.class)
                .timeout(Duration.ofSeconds(5))
                .map(response -> "OK".equals(response))
                .onErrorReturn(false);
    }
}
