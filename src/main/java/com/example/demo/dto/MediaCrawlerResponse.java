package com.example.demo.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * MediaCrawler响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MediaCrawlerResponse {
    
    /**
     * 响应状态
     */
    private String status;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 数据列表
     */
    private List<ContentData> data;
    
    /**
     * 总数
     */
    private Integer total;
    
    /**
     * 页码
     */
    private Integer page;
    
    /**
     * 页大小
     */
    private Integer pageSize;
    
    /**
     * 内容数据
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ContentData {
        
        /**
         * 内容ID
         */
        private String id;
        
        /**
         * 平台
         */
        private String platform;
        
        /**
         * 内容类型
         */
        private String type;
        
        /**
         * 标题
         */
        private String title;
        
        /**
         * 内容
         */
        private String content;
        
        /**
         * 作者
         */
        private String author;
        
        /**
         * 作者ID
         */
        private String authorId;
        
        /**
         * 发布时间
         */
        private String publishTime;
        
        /**
         * URL
         */
        private String url;
        
        /**
         * 点赞数
         */
        private Integer likeCount;
        
        /**
         * 评论数
         */
        private Integer commentCount;
        
        /**
         * 分享数
         */
        private Integer shareCount;
        
        /**
         * 浏览数
         */
        private Integer viewCount;
        
        /**
         * 图片URLs
         */
        private List<String> images;
        
        /**
         * 视频URL
         */
        private String videoUrl;
        
        /**
         * 标签
         */
        private List<String> tags;
        
        /**
         * 额外数据
         */
        private Map<String, Object> extraData;
    }
}
