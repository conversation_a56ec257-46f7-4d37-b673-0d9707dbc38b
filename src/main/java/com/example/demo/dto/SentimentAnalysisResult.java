package com.example.demo.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

/**
 * 情感分析结果DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SentimentAnalysisResult {
    
    /**
     * 情感类型 (positive, negative, neutral)
     */
    private String sentiment;
    
    /**
     * 置信度 (0-1)
     */
    private Double confidence;
    
    /**
     * 正面情感得分
     */
    private Double positiveScore;
    
    /**
     * 负面情感得分
     */
    private Double negativeScore;
    
    /**
     * 中性情感得分
     */
    private Double neutralScore;
    
    /**
     * 分析提供者
     */
    private String provider;
    
    /**
     * 分析时间戳
     */
    private Long timestamp;
    
    /**
     * 额外信息
     */
    private String extraInfo;
}
