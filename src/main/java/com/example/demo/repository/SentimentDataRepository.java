package com.example.demo.repository;

import com.example.demo.entity.SentimentData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 舆情数据Repository
 */
@Repository
public interface SentimentDataRepository extends JpaRepository<SentimentData, Long> {
    
    /**
     * 根据平台和内容ID查找
     */
    Optional<SentimentData> findByPlatformAndContentId(String platform, String contentId);
    
    /**
     * 根据平台查找
     */
    List<SentimentData> findByPlatform(String platform);
    
    /**
     * 根据情感类型查找
     */
    List<SentimentData> findBySentiment(String sentiment);
    
    /**
     * 根据时间范围查找
     */
    List<SentimentData> findByCrawlTimeBetween(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 根据平台和时间范围查找
     */
    List<SentimentData> findByPlatformAndCrawlTimeBetween(
            String platform, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 查找未处理的数据
     */
    List<SentimentData> findByIsProcessedFalse();
    
    /**
     * 查找未预警的负面情感数据
     */
    List<SentimentData> findBySentimentAndIsAlertedFalse(String sentiment);
    
    /**
     * 根据关键词模糊查找
     */
    @Query("SELECT s FROM SentimentData s WHERE s.content LIKE %:keyword% OR s.title LIKE %:keyword%")
    List<SentimentData> findByKeyword(@Param("keyword") String keyword);
    
    /**
     * 统计指定时间范围内的情感分布
     */
    @Query("SELECT s.sentiment, COUNT(s) FROM SentimentData s " +
           "WHERE s.crawlTime BETWEEN :startTime AND :endTime " +
           "GROUP BY s.sentiment")
    List<Object[]> countSentimentByTimeRange(
            @Param("startTime") LocalDateTime startTime, 
            @Param("endTime") LocalDateTime endTime);
    
    /**
     * 统计指定平台的情感分布
     */
    @Query("SELECT s.sentiment, COUNT(s) FROM SentimentData s " +
           "WHERE s.platform = :platform " +
           "GROUP BY s.sentiment")
    List<Object[]> countSentimentByPlatform(@Param("platform") String platform);
    
    /**
     * 查找热门内容 (根据互动数排序)
     */
    @Query("SELECT s FROM SentimentData s " +
           "WHERE s.crawlTime BETWEEN :startTime AND :endTime " +
           "ORDER BY (COALESCE(s.likeCount, 0) + COALESCE(s.commentCount, 0) + COALESCE(s.shareCount, 0)) DESC")
    List<SentimentData> findHotContentByTimeRange(
            @Param("startTime") LocalDateTime startTime, 
            @Param("endTime") LocalDateTime endTime);
    
    /**
     * 删除指定时间之前的数据
     */
    void deleteByCrawlTimeBefore(LocalDateTime time);
}
