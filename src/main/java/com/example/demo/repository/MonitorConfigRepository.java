package com.example.demo.repository;

import com.example.demo.entity.MonitorConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 监控配置Repository
 */
@Repository
public interface MonitorConfigRepository extends JpaRepository<MonitorConfig, Long> {
    
    /**
     * 查找启用的配置
     */
    List<MonitorConfig> findByIsEnabledTrue();
    
    /**
     * 根据平台查找配置
     */
    List<MonitorConfig> findByPlatform(String platform);
    
    /**
     * 查找需要执行的任务
     */
    List<MonitorConfig> findByIsEnabledTrueAndNextExecutionTimeBefore(LocalDateTime time);
    
    /**
     * 根据名称查找配置
     */
    MonitorConfig findByName(String name);
}
