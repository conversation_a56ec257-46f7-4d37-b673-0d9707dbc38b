package com.example.demo.repository;

import com.example.demo.entity.AlertRule;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 预警规则Repository
 */
@Repository
public interface AlertRuleRepository extends JpaRepository<AlertRule, Long> {
    
    /**
     * 查找启用的规则
     */
    List<AlertRule> findByIsEnabledTrue();
    
    /**
     * 根据规则类型查找
     */
    List<AlertRule> findByRuleTypeAndIsEnabledTrue(String ruleType);
    
    /**
     * 根据名称查找规则
     */
    AlertRule findByName(String name);
}
