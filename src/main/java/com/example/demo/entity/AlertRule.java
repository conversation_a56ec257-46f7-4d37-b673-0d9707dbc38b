package com.example.demo.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;

/**
 * 预警规则实体类
 */
@Entity
@Table(name = "alert_rule")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AlertRule {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 规则名称
     */
    @Column(nullable = false, length = 100)
    private String name;
    
    /**
     * 规则描述
     */
    @Column(length = 500)
    private String description;
    
    /**
     * 规则类型 (sentiment, keyword, volume, etc.)
     */
    @Column(name = "rule_type", nullable = false, length = 50)
    private String ruleType;
    
    /**
     * 平台限制 (空表示所有平台)
     */
    @Column(length = 200)
    private String platforms;
    
    /**
     * 关键词限制 (JSON数组格式)
     */
    @Column(columnDefinition = "TEXT")
    private String keywords;
    
    /**
     * 阈值配置 (JSON格式)
     */
    @Column(name = "threshold_config", columnDefinition = "TEXT")
    private String thresholdConfig;
    
    /**
     * 时间窗口 (分钟)
     */
    @Column(name = "time_window_minutes")
    private Integer timeWindowMinutes;
    
    /**
     * 是否启用
     */
    @Column(name = "is_enabled")
    private Boolean isEnabled = true;
    
    /**
     * 通知方式 (email, webhook, sms等，JSON数组格式)
     */
    @Column(name = "notification_methods", length = 200)
    private String notificationMethods;
    
    /**
     * 通知配置 (JSON格式)
     */
    @Column(name = "notification_config", columnDefinition = "TEXT")
    private String notificationConfig;
    
    /**
     * 最后触发时间
     */
    @Column(name = "last_triggered_time")
    private LocalDateTime lastTriggeredTime;
    
    /**
     * 触发次数
     */
    @Column(name = "trigger_count")
    private Integer triggerCount = 0;
    
    /**
     * 创建时间
     */
    @Column(name = "created_time", nullable = false)
    private LocalDateTime createdTime;
    
    /**
     * 更新时间
     */
    @Column(name = "updated_time")
    private LocalDateTime updatedTime;
    
    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        if (createdTime == null) {
            createdTime = now;
        }
        if (updatedTime == null) {
            updatedTime = now;
        }
        if (isEnabled == null) {
            isEnabled = true;
        }
        if (triggerCount == null) {
            triggerCount = 0;
        }
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedTime = LocalDateTime.now();
    }
}
