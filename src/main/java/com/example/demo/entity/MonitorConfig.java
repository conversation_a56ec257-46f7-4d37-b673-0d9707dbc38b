package com.example.demo.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;

/**
 * 监控配置实体类
 */
@Entity
@Table(name = "monitor_config")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MonitorConfig {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 配置名称
     */
    @Column(nullable = false, length = 100)
    private String name;
    
    /**
     * 平台名称
     */
    @Column(nullable = false, length = 50)
    private String platform;
    
    /**
     * 监控关键词 (JSON数组格式)
     */
    @Column(name = "keywords", columnDefinition = "TEXT")
    private String keywords;
    
    /**
     * 监控间隔 (秒)
     */
    @Column(name = "interval_seconds")
    private Integer intervalSeconds;
    
    /**
     * 是否启用
     */
    @Column(name = "is_enabled")
    private Boolean isEnabled = true;
    
    /**
     * 最后执行时间
     */
    @Column(name = "last_execution_time")
    private LocalDateTime lastExecutionTime;
    
    /**
     * 下次执行时间
     */
    @Column(name = "next_execution_time")
    private LocalDateTime nextExecutionTime;
    
    /**
     * 创建时间
     */
    @Column(name = "created_time", nullable = false)
    private LocalDateTime createdTime;
    
    /**
     * 更新时间
     */
    @Column(name = "updated_time")
    private LocalDateTime updatedTime;
    
    /**
     * 额外配置 (JSON格式)
     */
    @Column(name = "extra_config", columnDefinition = "TEXT")
    private String extraConfig;
    
    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        if (createdTime == null) {
            createdTime = now;
        }
        if (updatedTime == null) {
            updatedTime = now;
        }
        if (isEnabled == null) {
            isEnabled = true;
        }
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedTime = LocalDateTime.now();
    }
}
