package com.example.demo.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;

/**
 * 舆情数据实体类
 */
@Entity
@Table(name = "sentiment_data")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SentimentData {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 平台名称 (xiaohongshu, douyin, weibo, bilibili等)
     */
    @Column(nullable = false, length = 50)
    private String platform;
    
    /**
     * 内容ID (来自原平台)
     */
    @Column(name = "content_id", nullable = false, length = 100)
    private String contentId;
    
    /**
     * 内容类型 (post, comment, video等)
     */
    @Column(name = "content_type", length = 20)
    private String contentType;
    
    /**
     * 内容标题
     */
    @Column(length = 500)
    private String title;
    
    /**
     * 内容正文
     */
    @Column(columnDefinition = "TEXT")
    private String content;
    
    /**
     * 作者信息
     */
    @Column(length = 100)
    private String author;
    
    /**
     * 发布时间
     */
    @Column(name = "publish_time")
    private LocalDateTime publishTime;
    
    /**
     * 采集时间
     */
    @Column(name = "crawl_time", nullable = false)
    private LocalDateTime crawlTime;
    
    /**
     * 情感分析结果 (positive, negative, neutral)
     */
    @Column(length = 20)
    private String sentiment;
    
    /**
     * 情感分析置信度 (0-1)
     */
    @Column(name = "sentiment_score")
    private Double sentimentScore;
    
    /**
     * 点赞数
     */
    @Column(name = "like_count")
    private Integer likeCount;
    
    /**
     * 评论数
     */
    @Column(name = "comment_count")
    private Integer commentCount;
    
    /**
     * 分享数
     */
    @Column(name = "share_count")
    private Integer shareCount;
    
    /**
     * 浏览数
     */
    @Column(name = "view_count")
    private Integer viewCount;
    
    /**
     * 匹配的关键词
     */
    @Column(name = "matched_keywords", length = 500)
    private String matchedKeywords;
    
    /**
     * 原始数据URL
     */
    @Column(name = "source_url", length = 500)
    private String sourceUrl;
    
    /**
     * 是否已处理
     */
    @Column(name = "is_processed")
    @Builder.Default
    private Boolean isProcessed = false;

    /**
     * 是否触发预警
     */
    @Column(name = "is_alerted")
    @Builder.Default
    private Boolean isAlerted = false;
    
    /**
     * 额外数据 (JSON格式)
     */
    @Column(name = "extra_data", columnDefinition = "TEXT")
    private String extraData;
    
    @PrePersist
    protected void onCreate() {
        if (crawlTime == null) {
            crawlTime = LocalDateTime.now();
        }
        if (isProcessed == null) {
            isProcessed = false;
        }
        if (isAlerted == null) {
            isAlerted = false;
        }
    }
}
