package com.example.demo;

import com.example.demo.entity.SentimentData;
import com.example.demo.repository.SentimentDataRepository;
import com.example.demo.service.SentimentAnalysisService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles("test")
class SentimentMonitorApplicationTests {

    @Autowired
    private SentimentDataRepository sentimentDataRepository;
    
    @Autowired
    private SentimentAnalysisService sentimentAnalysisService;

    @Test
    void contextLoads() {
        // 测试Spring上下文是否正常加载
        assertNotNull(sentimentDataRepository);
        assertNotNull(sentimentAnalysisService);
    }
    
    @Test
    void testSentimentDataRepository() {
        // 测试数据库操作
        SentimentData testData = SentimentData.builder()
                .platform("test")
                .contentId("test-001")
                .contentType("post")
                .title("测试标题")
                .content("这是一个测试内容")
                .author("测试用户")
                .publishTime(LocalDateTime.now())
                .crawlTime(LocalDateTime.now())
                .sentiment("neutral")
                .sentimentScore(0.5)
                .isProcessed(true)
                .isAlerted(false)
                .build();
        
        SentimentData saved = sentimentDataRepository.save(testData);
        assertNotNull(saved.getId());
        assertEquals("test", saved.getPlatform());
        
        // 测试查询
        var found = sentimentDataRepository.findByPlatformAndContentId("test", "test-001");
        assertTrue(found.isPresent());
        assertEquals("测试标题", found.get().getTitle());
        
        // 清理测试数据
        sentimentDataRepository.delete(saved);
    }
    
    @Test
    void testSentimentAnalysis() {
        // 测试正面情感
        var positiveResult = sentimentAnalysisService.analyzeSentiment("这个产品真的很好，我很喜欢！👍");
        assertNotNull(positiveResult);
        assertEquals("positive", positiveResult.getSentiment());
        assertTrue(positiveResult.getConfidence() > 0);
        
        // 测试负面情感
        var negativeResult = sentimentAnalysisService.analyzeSentiment("这个产品太差了，完全不推荐！👎");
        assertNotNull(negativeResult);
        assertEquals("negative", negativeResult.getSentiment());
        assertTrue(negativeResult.getConfidence() > 0);
        
        // 测试中性情感
        var neutralResult = sentimentAnalysisService.analyzeSentiment("今天天气不错");
        assertNotNull(neutralResult);
        assertEquals("neutral", neutralResult.getSentiment());
    }
}
