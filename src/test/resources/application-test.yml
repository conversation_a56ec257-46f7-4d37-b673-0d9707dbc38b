spring:
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
  
  h2:
    console:
      enabled: false

logging:
  level:
    com.example.demo: INFO
    org.springframework: WARN
    org.hibernate: WARN

sentiment:
  monitor:
    mediacrawler:
      base-url: "http://localhost:8000"
      timeout: 5000
      retry-count: 1
    
    sentiment-analysis:
      enabled: true
      provider: "local"
      confidence-threshold: 0.6
    
    alert:
      enabled: false
