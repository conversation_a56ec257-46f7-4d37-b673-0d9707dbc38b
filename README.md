# 舆情监控系统

基于MediaCrawler的实时舆情监控框架，支持多平台数据采集、情感分析和智能预警。

## 功能特性

### 🚀 核心功能
- **多平台数据采集**: 支持小红书、抖音、微博、B站等主流社交媒体平台
- **实时情感分析**: 内置情感分析引擎，支持正面、负面、中性情感识别
- **智能预警系统**: 基于规则的预警机制，支持多种通知方式
- **关键词监控**: 灵活的关键词配置，支持品牌、产品、竞品监控
- **统计分析**: 丰富的数据统计和可视化分析
- **RESTful API**: 完整的API接口，支持第三方集成

### 📊 数据源
- **小红书**: 笔记、评论数据
- **抖音**: 视频、评论数据
- **微博**: 帖子、评论数据
- **B站**: 视频、评论数据

### 🔔 预警类型
- **情感预警**: 负面情感内容超过阈值时预警
- **关键词预警**: 关键词提及量异常时预警
- **数量预警**: 内容数量异常时预警

## 技术架构

### 后端技术栈
- **Spring Boot 3.5.3**: 主框架
- **Spring Data JPA**: 数据持久化
- **H2/MySQL**: 数据库支持
- **WebFlux**: 异步HTTP客户端
- **Jackson**: JSON处理
- **Lombok**: 代码简化

### 系统架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   MediaCrawler  │    │  舆情监控系统    │    │   通知系统      │
│   数据采集服务   │───▶│                │───▶│  (邮件/Webhook) │
└─────────────────┘    │  - 数据处理     │    └─────────────────┘
                       │  - 情感分析     │
┌─────────────────┐    │  - 预警检测     │    ┌─────────────────┐
│   定时任务      │───▶│  - 统计分析     │───▶│   API接口       │
│   调度系统      │    └─────────────────┘    │   对外服务      │
└─────────────────┘                          └─────────────────┘
```

## 快速开始

### 环境要求
- Java 17+
- Maven 3.6+
- MediaCrawler服务 (可选)

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd sentiment-monitor
```

2. **配置数据库**
```yaml
# application.yml
spring:
  datasource:
    url: jdbc:h2:mem:testdb  # 开发环境使用H2
    # url: *********************************************  # 生产环境使用MySQL
```

3. **配置MediaCrawler**
```yaml
# application.yml
sentiment:
  monitor:
    mediacrawler:
      base-url: "http://localhost:8000"  # MediaCrawler服务地址
```

4. **启动应用**
```bash
mvn spring-boot:run
```

5. **访问应用**
- API文档: http://localhost:8080/api
- H2控制台: http://localhost:8080/api/h2-console
- 健康检查: http://localhost:8080/api/system/health

## API接口

### 舆情数据管理
```http
GET    /api/sentiment/data              # 获取舆情数据列表
GET    /api/sentiment/data/{id}         # 获取单条舆情数据
GET    /api/sentiment/search            # 关键词搜索
POST   /api/sentiment/collect           # 手动采集数据
POST   /api/sentiment/analyze           # 情感分析
GET    /api/sentiment/stats/sentiment   # 情感统计
GET    /api/sentiment/stats/platform    # 平台统计
GET    /api/sentiment/hot               # 热门内容
```

### 配置管理
```http
GET    /api/config/monitor              # 获取监控配置
POST   /api/config/monitor              # 创建监控配置
PUT    /api/config/monitor/{id}         # 更新监控配置
DELETE /api/config/monitor/{id}         # 删除监控配置

GET    /api/config/alert                # 获取预警规则
POST   /api/config/alert                # 创建预警规则
PUT    /api/config/alert/{id}           # 更新预警规则
DELETE /api/config/alert/{id}           # 删除预警规则
```

### 系统状态
```http
GET    /api/system/health               # 健康检查
GET    /api/system/stats                # 系统统计
GET    /api/system/info                 # 系统信息
GET    /api/system/activity             # 系统活动
```

## 配置说明

### 监控配置示例
```json
{
  "name": "小红书品牌监控",
  "platform": "xiaohongshu",
  "keywords": ["品牌名称", "产品名称"],
  "intervalSeconds": 300,
  "isEnabled": true
}
```

### 预警规则示例
```json
{
  "name": "负面情感预警",
  "ruleType": "sentiment",
  "platforms": "xiaohongshu,douyin,weibo",
  "thresholdConfig": {
    "negative_threshold": 0.7,
    "min_count": 5
  },
  "timeWindowMinutes": 60,
  "notificationMethods": ["email", "webhook"]
}
```

## 部署指南

### Docker部署
```dockerfile
FROM openjdk:17-jre-slim
COPY target/sentiment-monitor.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

### 生产环境配置
```yaml
# application-prod.yml
spring:
  datasource:
    url: *****************************************
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
  jpa:
    hibernate:
      ddl-auto: validate

sentiment:
  monitor:
    mediacrawler:
      base-url: ${MEDIACRAWLER_URL}
    alert:
      notification:
        email: true
        webhook: true
```

## 开发指南

### 添加新平台支持
1. 在MediaCrawlerClient中添加新平台方法
2. 更新DataCollectionService的平台判断逻辑
3. 在配置文件中添加平台配置

### 扩展情感分析
1. 实现SentimentAnalysisService中的API调用方法
2. 添加新的情感分析提供者配置
3. 更新情感词典和分析算法

### 自定义预警规则
1. 在AlertService中添加新的规则类型处理
2. 实现对应的检测逻辑
3. 添加新的通知方式

## 监控和运维

### 日志配置
```yaml
logging:
  level:
    com.example.demo: DEBUG
  file:
    name: logs/sentiment-monitor.log
```

### 健康检查
系统提供多个健康检查端点：
- `/api/system/health`: 整体健康状态
- `/api/actuator/health`: Spring Boot健康检查
- `/api/actuator/metrics`: 系统指标

### 性能优化
- 数据库索引优化
- 缓存策略配置
- 异步处理优化
- 批量数据处理

## 常见问题

### Q: MediaCrawler连接失败怎么办？
A: 检查MediaCrawler服务是否正常运行，确认网络连接和配置地址正确。

### Q: 情感分析准确率如何提升？
A: 可以集成第三方情感分析API（百度、腾讯等），或训练自定义模型。

### Q: 如何处理大量数据？
A: 配置数据清理任务，使用数据库分区，考虑使用消息队列异步处理。

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交代码
4. 创建Pull Request

## 许可证

MIT License

## 联系方式

- 项目地址: [GitHub Repository]
- 问题反馈: [Issues]
- 技术支持: [Email]
