2025-07-18 14:27:36 [main] INFO  c.example.demo.Demo1ApplicationTests - Starting Demo1ApplicationTests using Java 17.0.1 with PID 17332 (started by wangjp in C:\Users\<USER>\Desktop\idea\demo1)
2025-07-18 14:27:36 [main] DEBUG c.example.demo.Demo1ApplicationTests - Running with Spring Boot v3.5.3, Spring v6.2.8
2025-07-18 14:27:36 [main] INFO  c.example.demo.Demo1ApplicationTests - No active profile set, falling back to 1 default profile: "default"
2025-07-18 14:27:38 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-18 14:27:38 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 230 ms. Found 3 JPA repository interfaces.
2025-07-18 14:27:39 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-18 14:27:39 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.6.18.Final
2025-07-18 14:27:39 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-18 14:27:40 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-18 14:27:40 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-18 14:27:40 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection conn0: url=jdbc:h2:mem:testdb user=SA
2025-07-18 14:27:40 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-18 14:27:40 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: H2Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-18 14:27:40 [main] INFO  o.hibernate.orm.connections.pooling - HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 2.3.232
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-07-18 14:27:41 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-18 14:27:42 [main] DEBUG org.hibernate.SQL - 
    drop table if exists alert_rule cascade 
2025-07-18 14:27:42 [main] DEBUG org.hibernate.SQL - 
    drop table if exists monitor_config cascade 
2025-07-18 14:27:42 [main] DEBUG org.hibernate.SQL - 
    drop table if exists sentiment_data cascade 
2025-07-18 14:27:42 [main] DEBUG org.hibernate.SQL - 
    create table alert_rule (
        is_enabled boolean,
        time_window_minutes integer,
        trigger_count integer,
        created_time timestamp(6) not null,
        id bigint generated by default as identity,
        last_triggered_time timestamp(6),
        updated_time timestamp(6),
        rule_type varchar(50) not null,
        name varchar(100) not null,
        notification_methods varchar(200),
        platforms varchar(200),
        description varchar(500),
        keywords TEXT,
        notification_config TEXT,
        threshold_config TEXT,
        primary key (id)
    )
2025-07-18 14:27:42 [main] DEBUG org.hibernate.SQL - 
    create table monitor_config (
        interval_seconds integer,
        is_enabled boolean,
        created_time timestamp(6) not null,
        id bigint generated by default as identity,
        last_execution_time timestamp(6),
        next_execution_time timestamp(6),
        updated_time timestamp(6),
        platform varchar(50) not null,
        name varchar(100) not null,
        extra_config TEXT,
        keywords TEXT,
        primary key (id)
    )
2025-07-18 14:27:42 [main] DEBUG org.hibernate.SQL - 
    create table sentiment_data (
        comment_count integer,
        is_alerted boolean,
        is_processed boolean,
        like_count integer,
        sentiment_score float(53),
        share_count integer,
        view_count integer,
        crawl_time timestamp(6) not null,
        id bigint generated by default as identity,
        publish_time timestamp(6),
        content_type varchar(20),
        sentiment varchar(20),
        platform varchar(50) not null,
        author varchar(100),
        content_id varchar(100) not null,
        matched_keywords varchar(500),
        source_url varchar(500),
        title varchar(500),
        content TEXT,
        extra_data TEXT,
        primary key (id)
    )
2025-07-18 14:27:42 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-18 14:27:42 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-18 14:27:44 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-18 14:27:44 [main] INFO  o.s.b.a.h.H2ConsoleAutoConfiguration - H2 console available at '/h2-console'. Database available at 'jdbc:h2:mem:testdb'
2025-07-18 14:27:44 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-07-18 14:27:44 [main] INFO  c.example.demo.Demo1ApplicationTests - Started Demo1ApplicationTests in 8.93 seconds (process running for 10.717)
2025-07-18 14:27:44 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - ��ʼִ�����ݲɼ�����
2025-07-18 14:27:45 [main] INFO  c.e.demo.config.DataInitializer - ��ʼ��ʼ��ϵͳ����...
2025-07-18 14:27:45 [main] DEBUG org.hibernate.SQL - 
    select
        count(*) 
    from
        monitor_config mc1_0
2025-07-18 14:27:45 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        mc1_0.id,
        mc1_0.created_time,
        mc1_0.extra_config,
        mc1_0.interval_seconds,
        mc1_0.is_enabled,
        mc1_0.keywords,
        mc1_0.last_execution_time,
        mc1_0.name,
        mc1_0.next_execution_time,
        mc1_0.platform,
        mc1_0.updated_time 
    from
        monitor_config mc1_0 
    where
        mc1_0.is_enabled 
        and mc1_0.next_execution_time<?
2025-07-18 14:27:45 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - û����Ҫִ�еļ������
2025-07-18 14:27:45 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - ��ʼִ��Ԥ���������
2025-07-18 14:27:45 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        ar1_0.id,
        ar1_0.created_time,
        ar1_0.description,
        ar1_0.is_enabled,
        ar1_0.keywords,
        ar1_0.last_triggered_time,
        ar1_0.name,
        ar1_0.notification_config,
        ar1_0.notification_methods,
        ar1_0.platforms,
        ar1_0.rule_type,
        ar1_0.threshold_config,
        ar1_0.time_window_minutes,
        ar1_0.trigger_count,
        ar1_0.updated_time 
    from
        alert_rule ar1_0 
    where
        ar1_0.is_enabled
2025-07-18 14:27:45 [scheduling-1] INFO  c.example.demo.service.AlertService - ��ʼ���0��Ԥ������
2025-07-18 14:27:45 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - ��ʼִ��ϵͳ�������
2025-07-18 14:27:45 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        count(*) 
    from
        sentiment_data sd1_0
2025-07-18 14:27:45 [scheduling-1] INFO  c.e.d.service.ScheduledTaskService - ��ǰ���ݿ��й���0����������
2025-07-18 14:27:45 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        sd1_0.id,
        sd1_0.author,
        sd1_0.comment_count,
        sd1_0.content,
        sd1_0.content_id,
        sd1_0.content_type,
        sd1_0.crawl_time,
        sd1_0.extra_data,
        sd1_0.is_alerted,
        sd1_0.is_processed,
        sd1_0.like_count,
        sd1_0.matched_keywords,
        sd1_0.platform,
        sd1_0.publish_time,
        sd1_0.sentiment,
        sd1_0.sentiment_score,
        sd1_0.share_count,
        sd1_0.source_url,
        sd1_0.title,
        sd1_0.view_count 
    from
        sentiment_data sd1_0 
    where
        sd1_0.crawl_time between ? and ?
2025-07-18 14:27:45 [scheduling-1] WARN  c.e.d.service.ScheduledTaskService - ���1Сʱû�вɼ��������ݣ�����ɼ�����
2025-07-18 14:27:45 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        mc1_0.id,
        mc1_0.created_time,
        mc1_0.extra_config,
        mc1_0.interval_seconds,
        mc1_0.is_enabled,
        mc1_0.keywords,
        mc1_0.last_execution_time,
        mc1_0.name,
        mc1_0.next_execution_time,
        mc1_0.platform,
        mc1_0.updated_time 
    from
        monitor_config mc1_0 
    where
        mc1_0.is_enabled
2025-07-18 14:27:45 [scheduling-1] INFO  c.e.d.service.ScheduledTaskService - ��ǰ��0�����õļ������
2025-07-18 14:27:45 [main] DEBUG org.hibernate.SQL - 
    insert 
    into
        monitor_config
        (created_time, extra_config, interval_seconds, is_enabled, keywords, last_execution_time, name, next_execution_time, platform, updated_time, id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-07-18 14:27:45 [main] DEBUG org.hibernate.SQL - 
    insert 
    into
        monitor_config
        (created_time, extra_config, interval_seconds, is_enabled, keywords, last_execution_time, name, next_execution_time, platform, updated_time, id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-07-18 14:27:45 [main] DEBUG org.hibernate.SQL - 
    insert 
    into
        monitor_config
        (created_time, extra_config, interval_seconds, is_enabled, keywords, last_execution_time, name, next_execution_time, platform, updated_time, id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-07-18 14:27:45 [main] INFO  c.e.demo.config.DataInitializer - ��ʼ����3���������
2025-07-18 14:27:45 [main] DEBUG org.hibernate.SQL - 
    select
        count(*) 
    from
        alert_rule ar1_0
2025-07-18 14:27:45 [main] DEBUG org.hibernate.SQL - 
    insert 
    into
        alert_rule
        (created_time, description, is_enabled, keywords, last_triggered_time, name, notification_config, notification_methods, platforms, rule_type, threshold_config, time_window_minutes, trigger_count, updated_time, id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-07-18 14:27:45 [main] DEBUG org.hibernate.SQL - 
    insert 
    into
        alert_rule
        (created_time, description, is_enabled, keywords, last_triggered_time, name, notification_config, notification_methods, platforms, rule_type, threshold_config, time_window_minutes, trigger_count, updated_time, id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-07-18 14:27:45 [main] DEBUG org.hibernate.SQL - 
    insert 
    into
        alert_rule
        (created_time, description, is_enabled, keywords, last_triggered_time, name, notification_config, notification_methods, platforms, rule_type, threshold_config, time_window_minutes, trigger_count, updated_time, id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-07-18 14:27:45 [main] INFO  c.e.demo.config.DataInitializer - ��ʼ����3��Ԥ������
2025-07-18 14:27:45 [main] INFO  c.e.demo.config.DataInitializer - ϵͳ���ݳ�ʼ�����
2025-07-18 14:27:46 [main] INFO  o.s.t.c.s.AnnotationConfigContextLoaderUtils - Could not detect default configuration classes for test class [com.example.demo.SentimentMonitorApplicationTests]: SentimentMonitorApplicationTests does not declare any static, non-private, non-final, nested classes annotated with @Configuration.
2025-07-18 14:27:46 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Found @SpringBootConfiguration com.example.demo.Demo1Application for test class com.example.demo.SentimentMonitorApplicationTests
2025-07-18 14:27:46 [main] INFO  c.e.d.SentimentMonitorApplicationTests - Starting SentimentMonitorApplicationTests using Java 17.0.1 with PID 17332 (started by wangjp in C:\Users\<USER>\Desktop\idea\demo1)
2025-07-18 14:27:46 [main] INFO  c.e.d.SentimentMonitorApplicationTests - The following 1 profile is active: "test"
2025-07-18 14:27:46 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Starting...
2025-07-18 14:27:46 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-2 - Added connection conn10: url=jdbc:h2:mem:testdb user=SA
2025-07-18 14:27:46 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Start completed.
2025-07-18 14:27:46 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: H2Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-18 14:27:46 [main] DEBUG org.hibernate.SQL - 
    drop table if exists alert_rule cascade 
2025-07-18 14:27:46 [main] DEBUG org.hibernate.SQL - 
    drop table if exists monitor_config cascade 
2025-07-18 14:27:46 [main] DEBUG org.hibernate.SQL - 
    drop table if exists sentiment_data cascade 
2025-07-18 14:27:46 [main] DEBUG org.hibernate.SQL - 
    create table alert_rule (
        is_enabled boolean,
        time_window_minutes integer,
        trigger_count integer,
        created_time timestamp(6) not null,
        id bigint generated by default as identity,
        last_triggered_time timestamp(6),
        updated_time timestamp(6),
        rule_type varchar(50) not null,
        name varchar(100) not null,
        notification_methods varchar(200),
        platforms varchar(200),
        description varchar(500),
        keywords TEXT,
        notification_config TEXT,
        threshold_config TEXT,
        primary key (id)
    )
2025-07-18 14:27:46 [main] DEBUG org.hibernate.SQL - 
    create table monitor_config (
        interval_seconds integer,
        is_enabled boolean,
        created_time timestamp(6) not null,
        id bigint generated by default as identity,
        last_execution_time timestamp(6),
        next_execution_time timestamp(6),
        updated_time timestamp(6),
        platform varchar(50) not null,
        name varchar(100) not null,
        extra_config TEXT,
        keywords TEXT,
        primary key (id)
    )
2025-07-18 14:27:46 [main] DEBUG org.hibernate.SQL - 
    create table sentiment_data (
        comment_count integer,
        is_alerted boolean,
        is_processed boolean,
        like_count integer,
        sentiment_score float(53),
        share_count integer,
        view_count integer,
        crawl_time timestamp(6) not null,
        id bigint generated by default as identity,
        publish_time timestamp(6),
        content_type varchar(20),
        sentiment varchar(20),
        platform varchar(50) not null,
        author varchar(100),
        content_id varchar(100) not null,
        matched_keywords varchar(500),
        source_url varchar(500),
        title varchar(500),
        content TEXT,
        extra_data TEXT,
        primary key (id)
    )
2025-07-18 14:27:47 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-18 14:27:47 [main] INFO  c.e.d.SentimentMonitorApplicationTests - Started SentimentMonitorApplicationTests in 0.941 seconds (process running for 13.026)
2025-07-18 14:27:47 [main] INFO  c.e.demo.config.DataInitializer - ��ʼ��ʼ��ϵͳ����...
2025-07-18 14:27:47 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        mc1_0.id,
        mc1_0.created_time,
        mc1_0.extra_config,
        mc1_0.interval_seconds,
        mc1_0.is_enabled,
        mc1_0.keywords,
        mc1_0.last_execution_time,
        mc1_0.name,
        mc1_0.next_execution_time,
        mc1_0.platform,
        mc1_0.updated_time 
    from
        monitor_config mc1_0 
    where
        mc1_0.is_enabled 
        and mc1_0.next_execution_time<?
2025-07-18 14:27:47 [main] DEBUG org.hibernate.SQL - 
    select
        count(*) 
    from
        monitor_config mc1_0
2025-07-18 14:27:47 [main] DEBUG org.hibernate.SQL - 
    insert 
    into
        monitor_config
        (created_time, extra_config, interval_seconds, is_enabled, keywords, last_execution_time, name, next_execution_time, platform, updated_time, id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-07-18 14:27:47 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        count(*) 
    from
        sentiment_data sd1_0
2025-07-18 14:27:47 [scheduling-1] INFO  c.e.d.service.ScheduledTaskService - ��ǰ���ݿ��й���0����������
2025-07-18 14:27:47 [main] DEBUG org.hibernate.SQL - 
    insert 
    into
        monitor_config
        (created_time, extra_config, interval_seconds, is_enabled, keywords, last_execution_time, name, next_execution_time, platform, updated_time, id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-07-18 14:27:47 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        sd1_0.id,
        sd1_0.author,
        sd1_0.comment_count,
        sd1_0.content,
        sd1_0.content_id,
        sd1_0.content_type,
        sd1_0.crawl_time,
        sd1_0.extra_data,
        sd1_0.is_alerted,
        sd1_0.is_processed,
        sd1_0.like_count,
        sd1_0.matched_keywords,
        sd1_0.platform,
        sd1_0.publish_time,
        sd1_0.sentiment,
        sd1_0.sentiment_score,
        sd1_0.share_count,
        sd1_0.source_url,
        sd1_0.title,
        sd1_0.view_count 
    from
        sentiment_data sd1_0 
    where
        sd1_0.crawl_time between ? and ?
2025-07-18 14:27:47 [scheduling-1] WARN  c.e.d.service.ScheduledTaskService - ���1Сʱû�вɼ��������ݣ�����ɼ�����
2025-07-18 14:27:47 [main] DEBUG org.hibernate.SQL - 
    insert 
    into
        monitor_config
        (created_time, extra_config, interval_seconds, is_enabled, keywords, last_execution_time, name, next_execution_time, platform, updated_time, id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-07-18 14:27:47 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        mc1_0.id,
        mc1_0.created_time,
        mc1_0.extra_config,
        mc1_0.interval_seconds,
        mc1_0.is_enabled,
        mc1_0.keywords,
        mc1_0.last_execution_time,
        mc1_0.name,
        mc1_0.next_execution_time,
        mc1_0.platform,
        mc1_0.updated_time 
    from
        monitor_config mc1_0 
    where
        mc1_0.is_enabled
2025-07-18 14:27:47 [main] INFO  c.e.demo.config.DataInitializer - ��ʼ����3���������
2025-07-18 14:27:47 [scheduling-1] INFO  c.e.d.service.ScheduledTaskService - ��ǰ��2�����õļ������
2025-07-18 14:27:47 [main] DEBUG org.hibernate.SQL - 
    select
        count(*) 
    from
        alert_rule ar1_0
2025-07-18 14:27:47 [main] DEBUG org.hibernate.SQL - 
    insert 
    into
        alert_rule
        (created_time, description, is_enabled, keywords, last_triggered_time, name, notification_config, notification_methods, platforms, rule_type, threshold_config, time_window_minutes, trigger_count, updated_time, id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-07-18 14:27:47 [main] DEBUG org.hibernate.SQL - 
    insert 
    into
        alert_rule
        (created_time, description, is_enabled, keywords, last_triggered_time, name, notification_config, notification_methods, platforms, rule_type, threshold_config, time_window_minutes, trigger_count, updated_time, id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-07-18 14:27:47 [main] DEBUG org.hibernate.SQL - 
    insert 
    into
        alert_rule
        (created_time, description, is_enabled, keywords, last_triggered_time, name, notification_config, notification_methods, platforms, rule_type, threshold_config, time_window_minutes, trigger_count, updated_time, id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-07-18 14:27:47 [main] INFO  c.e.demo.config.DataInitializer - ��ʼ����3��Ԥ������
2025-07-18 14:27:47 [main] INFO  c.e.demo.config.DataInitializer - ϵͳ���ݳ�ʼ�����
2025-07-18 14:27:47 [main] DEBUG org.hibernate.SQL - 
    insert 
    into
        sentiment_data
        (author, comment_count, content, content_id, content_type, crawl_time, extra_data, is_alerted, is_processed, like_count, matched_keywords, platform, publish_time, sentiment, sentiment_score, share_count, source_url, title, view_count, id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-07-18 14:27:47 [main] DEBUG org.hibernate.SQL - 
    select
        sd1_0.id,
        sd1_0.author,
        sd1_0.comment_count,
        sd1_0.content,
        sd1_0.content_id,
        sd1_0.content_type,
        sd1_0.crawl_time,
        sd1_0.extra_data,
        sd1_0.is_alerted,
        sd1_0.is_processed,
        sd1_0.like_count,
        sd1_0.matched_keywords,
        sd1_0.platform,
        sd1_0.publish_time,
        sd1_0.sentiment,
        sd1_0.sentiment_score,
        sd1_0.share_count,
        sd1_0.source_url,
        sd1_0.title,
        sd1_0.view_count 
    from
        sentiment_data sd1_0 
    where
        sd1_0.platform=? 
        and sd1_0.content_id=?
2025-07-18 14:27:47 [main] DEBUG org.hibernate.SQL - 
    select
        sd1_0.id,
        sd1_0.author,
        sd1_0.comment_count,
        sd1_0.content,
        sd1_0.content_id,
        sd1_0.content_type,
        sd1_0.crawl_time,
        sd1_0.extra_data,
        sd1_0.is_alerted,
        sd1_0.is_processed,
        sd1_0.like_count,
        sd1_0.matched_keywords,
        sd1_0.platform,
        sd1_0.publish_time,
        sd1_0.sentiment,
        sd1_0.sentiment_score,
        sd1_0.share_count,
        sd1_0.source_url,
        sd1_0.title,
        sd1_0.view_count 
    from
        sentiment_data sd1_0 
    where
        sd1_0.id=?
2025-07-18 14:27:47 [main] DEBUG org.hibernate.SQL - 
    delete 
    from
        sentiment_data 
    where
        id=?
2025-07-18 14:27:47 [SpringApplicationShutdownHook] DEBUG org.hibernate.SQL - 
    drop table if exists alert_rule cascade 
2025-07-18 14:27:47 [SpringApplicationShutdownHook] DEBUG org.hibernate.SQL - 
    drop table if exists monitor_config cascade 
2025-07-18 14:27:47 [SpringApplicationShutdownHook] DEBUG org.hibernate.SQL - 
    drop table if exists sentiment_data cascade 
2025-07-18 14:27:47 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-18 14:27:47 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-18 14:27:47 [SpringApplicationShutdownHook] DEBUG org.hibernate.SQL - 
    drop table if exists alert_rule cascade 
2025-07-18 14:27:47 [SpringApplicationShutdownHook] DEBUG org.hibernate.SQL - 
    drop table if exists monitor_config cascade 
2025-07-18 14:27:47 [SpringApplicationShutdownHook] DEBUG org.hibernate.SQL - 
    drop table if exists sentiment_data cascade 
2025-07-18 14:27:47 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Shutdown initiated...
2025-07-18 14:27:47 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Shutdown completed.
2025-07-18 14:28:27 [main] INFO  com.example.demo.Demo1Application - Starting Demo1Application using Java 17.0.1 with PID 16368 (C:\Users\<USER>\Desktop\idea\demo1\target\classes started by wangjp in C:\Users\<USER>\Desktop\idea\demo1)
2025-07-18 14:28:27 [main] DEBUG com.example.demo.Demo1Application - Running with Spring Boot v3.5.3, Spring v6.2.8
2025-07-18 14:28:27 [main] INFO  com.example.demo.Demo1Application - No active profile set, falling back to 1 default profile: "default"
2025-07-18 14:28:28 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-18 14:28:28 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 164 ms. Found 3 JPA repository interfaces.
2025-07-18 14:28:28 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-18 14:28:29 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 14:28:29 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-07-18 14:28:29 [main] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-18 14:28:29 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1936 ms
2025-07-18 14:28:29 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-18 14:28:29 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.6.18.Final
2025-07-18 14:28:29 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-18 14:28:29 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-18 14:28:29 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-18 14:28:30 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection conn0: url=jdbc:h2:mem:testdb user=SA
2025-07-18 14:28:30 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-18 14:28:30 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: H2Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-18 14:28:30 [main] INFO  o.hibernate.orm.connections.pooling - HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 2.3.232
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-07-18 14:28:31 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-18 14:28:31 [main] DEBUG org.hibernate.SQL - 
    drop table if exists alert_rule cascade 
2025-07-18 14:28:31 [main] DEBUG org.hibernate.SQL - 
    drop table if exists monitor_config cascade 
2025-07-18 14:28:31 [main] DEBUG org.hibernate.SQL - 
    drop table if exists sentiment_data cascade 
2025-07-18 14:28:31 [main] DEBUG org.hibernate.SQL - 
    create table alert_rule (
        is_enabled boolean,
        time_window_minutes integer,
        trigger_count integer,
        created_time timestamp(6) not null,
        id bigint generated by default as identity,
        last_triggered_time timestamp(6),
        updated_time timestamp(6),
        rule_type varchar(50) not null,
        name varchar(100) not null,
        notification_methods varchar(200),
        platforms varchar(200),
        description varchar(500),
        keywords TEXT,
        notification_config TEXT,
        threshold_config TEXT,
        primary key (id)
    )
2025-07-18 14:28:31 [main] DEBUG org.hibernate.SQL - 
    create table monitor_config (
        interval_seconds integer,
        is_enabled boolean,
        created_time timestamp(6) not null,
        id bigint generated by default as identity,
        last_execution_time timestamp(6),
        next_execution_time timestamp(6),
        updated_time timestamp(6),
        platform varchar(50) not null,
        name varchar(100) not null,
        extra_config TEXT,
        keywords TEXT,
        primary key (id)
    )
2025-07-18 14:28:31 [main] DEBUG org.hibernate.SQL - 
    create table sentiment_data (
        comment_count integer,
        is_alerted boolean,
        is_processed boolean,
        like_count integer,
        sentiment_score float(53),
        share_count integer,
        view_count integer,
        crawl_time timestamp(6) not null,
        id bigint generated by default as identity,
        publish_time timestamp(6),
        content_type varchar(20),
        sentiment varchar(20),
        platform varchar(50) not null,
        author varchar(100),
        content_id varchar(100) not null,
        matched_keywords varchar(500),
        source_url varchar(500),
        title varchar(500),
        content TEXT,
        extra_data TEXT,
        primary key (id)
    )
2025-07-18 14:28:31 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-18 14:28:31 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-18 14:28:32 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-18 14:28:33 [main] INFO  o.s.b.a.h.H2ConsoleAutoConfiguration - H2 console available at '/h2-console'. Database available at 'jdbc:h2:mem:testdb'
2025-07-18 14:28:33 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-07-18 14:28:33 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/api'
2025-07-18 14:28:33 [main] INFO  com.example.demo.Demo1Application - Started Demo1Application in 6.723 seconds (process running for 7.169)
2025-07-18 14:28:33 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - ��ʼִ�����ݲɼ�����
2025-07-18 14:28:33 [main] INFO  c.e.demo.config.DataInitializer - ��ʼ��ʼ��ϵͳ����...
2025-07-18 14:28:33 [main] DEBUG org.hibernate.SQL - 
    select
        count(*) 
    from
        monitor_config mc1_0
2025-07-18 14:28:33 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        mc1_0.id,
        mc1_0.created_time,
        mc1_0.extra_config,
        mc1_0.interval_seconds,
        mc1_0.is_enabled,
        mc1_0.keywords,
        mc1_0.last_execution_time,
        mc1_0.name,
        mc1_0.next_execution_time,
        mc1_0.platform,
        mc1_0.updated_time 
    from
        monitor_config mc1_0 
    where
        mc1_0.is_enabled 
        and mc1_0.next_execution_time<?
2025-07-18 14:28:33 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - û����Ҫִ�еļ������
2025-07-18 14:28:33 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - ��ʼִ��ϵͳ�������
2025-07-18 14:28:33 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        count(*) 
    from
        sentiment_data sd1_0
2025-07-18 14:28:33 [scheduling-1] INFO  c.e.d.service.ScheduledTaskService - ��ǰ���ݿ��й���0����������
2025-07-18 14:28:33 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        sd1_0.id,
        sd1_0.author,
        sd1_0.comment_count,
        sd1_0.content,
        sd1_0.content_id,
        sd1_0.content_type,
        sd1_0.crawl_time,
        sd1_0.extra_data,
        sd1_0.is_alerted,
        sd1_0.is_processed,
        sd1_0.like_count,
        sd1_0.matched_keywords,
        sd1_0.platform,
        sd1_0.publish_time,
        sd1_0.sentiment,
        sd1_0.sentiment_score,
        sd1_0.share_count,
        sd1_0.source_url,
        sd1_0.title,
        sd1_0.view_count 
    from
        sentiment_data sd1_0 
    where
        sd1_0.crawl_time between ? and ?
2025-07-18 14:28:33 [scheduling-1] WARN  c.e.d.service.ScheduledTaskService - ���1Сʱû�вɼ��������ݣ�����ɼ�����
2025-07-18 14:28:33 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        mc1_0.id,
        mc1_0.created_time,
        mc1_0.extra_config,
        mc1_0.interval_seconds,
        mc1_0.is_enabled,
        mc1_0.keywords,
        mc1_0.last_execution_time,
        mc1_0.name,
        mc1_0.next_execution_time,
        mc1_0.platform,
        mc1_0.updated_time 
    from
        monitor_config mc1_0 
    where
        mc1_0.is_enabled
2025-07-18 14:28:33 [scheduling-1] INFO  c.e.d.service.ScheduledTaskService - ��ǰ��0�����õļ������
2025-07-18 14:28:33 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - ��ʼִ��Ԥ���������
2025-07-18 14:28:33 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        ar1_0.id,
        ar1_0.created_time,
        ar1_0.description,
        ar1_0.is_enabled,
        ar1_0.keywords,
        ar1_0.last_triggered_time,
        ar1_0.name,
        ar1_0.notification_config,
        ar1_0.notification_methods,
        ar1_0.platforms,
        ar1_0.rule_type,
        ar1_0.threshold_config,
        ar1_0.time_window_minutes,
        ar1_0.trigger_count,
        ar1_0.updated_time 
    from
        alert_rule ar1_0 
    where
        ar1_0.is_enabled
2025-07-18 14:28:33 [scheduling-1] INFO  c.example.demo.service.AlertService - ��ʼ���0��Ԥ������
2025-07-18 14:28:33 [main] DEBUG org.hibernate.SQL - 
    insert 
    into
        monitor_config
        (created_time, extra_config, interval_seconds, is_enabled, keywords, last_execution_time, name, next_execution_time, platform, updated_time, id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-07-18 14:28:33 [main] DEBUG org.hibernate.SQL - 
    insert 
    into
        monitor_config
        (created_time, extra_config, interval_seconds, is_enabled, keywords, last_execution_time, name, next_execution_time, platform, updated_time, id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-07-18 14:28:33 [main] DEBUG org.hibernate.SQL - 
    insert 
    into
        monitor_config
        (created_time, extra_config, interval_seconds, is_enabled, keywords, last_execution_time, name, next_execution_time, platform, updated_time, id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-07-18 14:28:33 [main] INFO  c.e.demo.config.DataInitializer - ��ʼ����3���������
2025-07-18 14:28:33 [main] DEBUG org.hibernate.SQL - 
    select
        count(*) 
    from
        alert_rule ar1_0
2025-07-18 14:28:33 [main] DEBUG org.hibernate.SQL - 
    insert 
    into
        alert_rule
        (created_time, description, is_enabled, keywords, last_triggered_time, name, notification_config, notification_methods, platforms, rule_type, threshold_config, time_window_minutes, trigger_count, updated_time, id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-07-18 14:28:33 [main] DEBUG org.hibernate.SQL - 
    insert 
    into
        alert_rule
        (created_time, description, is_enabled, keywords, last_triggered_time, name, notification_config, notification_methods, platforms, rule_type, threshold_config, time_window_minutes, trigger_count, updated_time, id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-07-18 14:28:33 [main] DEBUG org.hibernate.SQL - 
    insert 
    into
        alert_rule
        (created_time, description, is_enabled, keywords, last_triggered_time, name, notification_config, notification_methods, platforms, rule_type, threshold_config, time_window_minutes, trigger_count, updated_time, id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-07-18 14:28:33 [main] INFO  c.e.demo.config.DataInitializer - ��ʼ����3��Ԥ������
2025-07-18 14:28:33 [main] INFO  c.e.demo.config.DataInitializer - ϵͳ���ݳ�ʼ�����
2025-07-18 14:29:33 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - ��ʼִ�����ݲɼ�����
2025-07-18 14:29:33 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        mc1_0.id,
        mc1_0.created_time,
        mc1_0.extra_config,
        mc1_0.interval_seconds,
        mc1_0.is_enabled,
        mc1_0.keywords,
        mc1_0.last_execution_time,
        mc1_0.name,
        mc1_0.next_execution_time,
        mc1_0.platform,
        mc1_0.updated_time 
    from
        monitor_config mc1_0 
    where
        mc1_0.is_enabled 
        and mc1_0.next_execution_time<?
2025-07-18 14:29:33 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - û����Ҫִ�еļ������
2025-07-18 14:29:46 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-18 14:29:46 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-18 14:29:46 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-18 14:29:47 [http-nio-8080-exec-1] DEBUG org.hibernate.SQL - 
    select
        count(*) 
    from
        sentiment_data sd1_0
2025-07-18 14:30:08 [http-nio-8080-exec-3] DEBUG org.hibernate.SQL - 
    select
        count(*) 
    from
        sentiment_data sd1_0
2025-07-18 14:30:08 [http-nio-8080-exec-3] DEBUG org.hibernate.SQL - 
    select
        sd1_0.id,
        sd1_0.author,
        sd1_0.comment_count,
        sd1_0.content,
        sd1_0.content_id,
        sd1_0.content_type,
        sd1_0.crawl_time,
        sd1_0.extra_data,
        sd1_0.is_alerted,
        sd1_0.is_processed,
        sd1_0.like_count,
        sd1_0.matched_keywords,
        sd1_0.platform,
        sd1_0.publish_time,
        sd1_0.sentiment,
        sd1_0.sentiment_score,
        sd1_0.share_count,
        sd1_0.source_url,
        sd1_0.title,
        sd1_0.view_count 
    from
        sentiment_data sd1_0 
    where
        sd1_0.crawl_time between ? and ?
2025-07-18 14:30:08 [http-nio-8080-exec-3] DEBUG org.hibernate.SQL - 
    select
        count(*) 
    from
        monitor_config mc1_0
2025-07-18 14:30:08 [http-nio-8080-exec-3] DEBUG org.hibernate.SQL - 
    select
        mc1_0.id,
        mc1_0.created_time,
        mc1_0.extra_config,
        mc1_0.interval_seconds,
        mc1_0.is_enabled,
        mc1_0.keywords,
        mc1_0.last_execution_time,
        mc1_0.name,
        mc1_0.next_execution_time,
        mc1_0.platform,
        mc1_0.updated_time 
    from
        monitor_config mc1_0 
    where
        mc1_0.is_enabled
2025-07-18 14:30:08 [http-nio-8080-exec-3] DEBUG org.hibernate.SQL - 
    select
        count(*) 
    from
        alert_rule ar1_0
2025-07-18 14:30:08 [http-nio-8080-exec-3] DEBUG org.hibernate.SQL - 
    select
        ar1_0.id,
        ar1_0.created_time,
        ar1_0.description,
        ar1_0.is_enabled,
        ar1_0.keywords,
        ar1_0.last_triggered_time,
        ar1_0.name,
        ar1_0.notification_config,
        ar1_0.notification_methods,
        ar1_0.platforms,
        ar1_0.rule_type,
        ar1_0.threshold_config,
        ar1_0.time_window_minutes,
        ar1_0.trigger_count,
        ar1_0.updated_time 
    from
        alert_rule ar1_0 
    where
        ar1_0.is_enabled
2025-07-18 14:30:08 [http-nio-8080-exec-3] DEBUG org.hibernate.SQL - 
    select
        sd1_0.sentiment,
        count(sd1_0.id) 
    from
        sentiment_data sd1_0 
    where
        sd1_0.crawl_time between ? and ? 
    group by
        sd1_0.sentiment
2025-07-18 14:30:33 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - ��ʼִ�����ݲɼ�����
2025-07-18 14:30:33 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        mc1_0.id,
        mc1_0.created_time,
        mc1_0.extra_config,
        mc1_0.interval_seconds,
        mc1_0.is_enabled,
        mc1_0.keywords,
        mc1_0.last_execution_time,
        mc1_0.name,
        mc1_0.next_execution_time,
        mc1_0.platform,
        mc1_0.updated_time 
    from
        monitor_config mc1_0 
    where
        mc1_0.is_enabled 
        and mc1_0.next_execution_time<?
2025-07-18 14:30:33 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - û����Ҫִ�еļ������
2025-07-18 14:31:33 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - ��ʼִ�����ݲɼ�����
2025-07-18 14:31:33 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        mc1_0.id,
        mc1_0.created_time,
        mc1_0.extra_config,
        mc1_0.interval_seconds,
        mc1_0.is_enabled,
        mc1_0.keywords,
        mc1_0.last_execution_time,
        mc1_0.name,
        mc1_0.next_execution_time,
        mc1_0.platform,
        mc1_0.updated_time 
    from
        monitor_config mc1_0 
    where
        mc1_0.is_enabled 
        and mc1_0.next_execution_time<?
2025-07-18 14:31:33 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - û����Ҫִ�еļ������
2025-07-18 14:32:33 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - ��ʼִ�����ݲɼ�����
2025-07-18 14:32:33 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        mc1_0.id,
        mc1_0.created_time,
        mc1_0.extra_config,
        mc1_0.interval_seconds,
        mc1_0.is_enabled,
        mc1_0.keywords,
        mc1_0.last_execution_time,
        mc1_0.name,
        mc1_0.next_execution_time,
        mc1_0.platform,
        mc1_0.updated_time 
    from
        monitor_config mc1_0 
    where
        mc1_0.is_enabled 
        and mc1_0.next_execution_time<?
2025-07-18 14:32:33 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - û����Ҫִ�еļ������
2025-07-18 14:33:33 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - ��ʼִ�����ݲɼ�����
2025-07-18 14:33:33 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        mc1_0.id,
        mc1_0.created_time,
        mc1_0.extra_config,
        mc1_0.interval_seconds,
        mc1_0.is_enabled,
        mc1_0.keywords,
        mc1_0.last_execution_time,
        mc1_0.name,
        mc1_0.next_execution_time,
        mc1_0.platform,
        mc1_0.updated_time 
    from
        monitor_config mc1_0 
    where
        mc1_0.is_enabled 
        and mc1_0.next_execution_time<?
2025-07-18 14:33:33 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - û����Ҫִ�еļ������
2025-07-18 14:33:33 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - ��ʼִ��Ԥ���������
2025-07-18 14:33:33 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        ar1_0.id,
        ar1_0.created_time,
        ar1_0.description,
        ar1_0.is_enabled,
        ar1_0.keywords,
        ar1_0.last_triggered_time,
        ar1_0.name,
        ar1_0.notification_config,
        ar1_0.notification_methods,
        ar1_0.platforms,
        ar1_0.rule_type,
        ar1_0.threshold_config,
        ar1_0.time_window_minutes,
        ar1_0.trigger_count,
        ar1_0.updated_time 
    from
        alert_rule ar1_0 
    where
        ar1_0.is_enabled
2025-07-18 14:33:33 [scheduling-1] INFO  c.example.demo.service.AlertService - ��ʼ���2��Ԥ������
2025-07-18 14:33:33 [scheduling-1] DEBUG c.example.demo.service.AlertService - ���Ԥ�����򣺸������Ԥ��
2025-07-18 14:33:33 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        sd1_0.id,
        sd1_0.author,
        sd1_0.comment_count,
        sd1_0.content,
        sd1_0.content_id,
        sd1_0.content_type,
        sd1_0.crawl_time,
        sd1_0.extra_data,
        sd1_0.is_alerted,
        sd1_0.is_processed,
        sd1_0.like_count,
        sd1_0.matched_keywords,
        sd1_0.platform,
        sd1_0.publish_time,
        sd1_0.sentiment,
        sd1_0.sentiment_score,
        sd1_0.share_count,
        sd1_0.source_url,
        sd1_0.title,
        sd1_0.view_count 
    from
        sentiment_data sd1_0 
    where
        sd1_0.sentiment=? 
        and not(sd1_0.is_alerted)
2025-07-18 14:33:33 [scheduling-1] DEBUG c.example.demo.service.AlertService - ���Ԥ�����򣺹ؼ����ἰ��Ԥ��
2025-07-18 14:33:33 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        sd1_0.id,
        sd1_0.author,
        sd1_0.comment_count,
        sd1_0.content,
        sd1_0.content_id,
        sd1_0.content_type,
        sd1_0.crawl_time,
        sd1_0.extra_data,
        sd1_0.is_alerted,
        sd1_0.is_processed,
        sd1_0.like_count,
        sd1_0.matched_keywords,
        sd1_0.platform,
        sd1_0.publish_time,
        sd1_0.sentiment,
        sd1_0.sentiment_score,
        sd1_0.share_count,
        sd1_0.source_url,
        sd1_0.title,
        sd1_0.view_count 
    from
        sentiment_data sd1_0 
    where
        sd1_0.content like ? escape '' 
        or sd1_0.title like ? escape ''
2025-07-18 14:33:33 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        sd1_0.id,
        sd1_0.author,
        sd1_0.comment_count,
        sd1_0.content,
        sd1_0.content_id,
        sd1_0.content_type,
        sd1_0.crawl_time,
        sd1_0.extra_data,
        sd1_0.is_alerted,
        sd1_0.is_processed,
        sd1_0.like_count,
        sd1_0.matched_keywords,
        sd1_0.platform,
        sd1_0.publish_time,
        sd1_0.sentiment,
        sd1_0.sentiment_score,
        sd1_0.share_count,
        sd1_0.source_url,
        sd1_0.title,
        sd1_0.view_count 
    from
        sentiment_data sd1_0 
    where
        sd1_0.content like ? escape '' 
        or sd1_0.title like ? escape ''
2025-07-18 14:34:33 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - ��ʼִ�����ݲɼ�����
2025-07-18 14:34:33 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        mc1_0.id,
        mc1_0.created_time,
        mc1_0.extra_config,
        mc1_0.interval_seconds,
        mc1_0.is_enabled,
        mc1_0.keywords,
        mc1_0.last_execution_time,
        mc1_0.name,
        mc1_0.next_execution_time,
        mc1_0.platform,
        mc1_0.updated_time 
    from
        monitor_config mc1_0 
    where
        mc1_0.is_enabled 
        and mc1_0.next_execution_time<?
2025-07-18 14:34:33 [scheduling-1] INFO  c.e.d.service.ScheduledTaskService - �ҵ�2����Ҫִ�еļ������
2025-07-18 14:34:33 [scheduling-1] INFO  c.e.d.service.DataCollectionService - ��ʼ�������òɼ����ݣ�С����Ʒ�Ƽ��
2025-07-18 14:34:33 [scheduling-1] INFO  c.e.demo.service.MediaCrawlerClient - ��ʼ��ȡxiaohongshu���ݣ��ؼ��ʣ�[Ʒ������, ��Ʒ����]�����ƣ�50
2025-07-18 14:34:39 [reactor-http-nio-6] ERROR c.e.demo.service.MediaCrawlerClient - ��ȡxiaohongshu����ʧ�ܣ�Retries exhausted: 3/3
2025-07-18 14:34:39 [scheduling-1] WARN  c.e.d.service.DataCollectionService - ����С����Ʒ�Ƽ�زɼ�ʧ�ܻ�������
2025-07-18 14:34:39 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        mc1_0.id,
        mc1_0.created_time,
        mc1_0.extra_config,
        mc1_0.interval_seconds,
        mc1_0.is_enabled,
        mc1_0.keywords,
        mc1_0.last_execution_time,
        mc1_0.name,
        mc1_0.next_execution_time,
        mc1_0.platform,
        mc1_0.updated_time 
    from
        monitor_config mc1_0 
    where
        mc1_0.id=?
2025-07-18 14:34:39 [scheduling-1] DEBUG org.hibernate.SQL - 
    update
        monitor_config 
    set
        created_time=?,
        extra_config=?,
        interval_seconds=?,
        is_enabled=?,
        keywords=?,
        last_execution_time=?,
        name=?,
        next_execution_time=?,
        platform=?,
        updated_time=? 
    where
        id=?
2025-07-18 14:34:39 [scheduling-1] INFO  c.e.d.service.DataCollectionService - ��ʼ�������òɼ����ݣ�΢��Ʒ�Ƽ��
2025-07-18 14:34:39 [scheduling-1] INFO  c.e.demo.service.MediaCrawlerClient - ��ʼ��ȡweibo���ݣ��ؼ��ʣ�[Ʒ������, ��Ʒ����]�����ƣ�50
2025-07-18 14:34:45 [reactor-http-nio-2] ERROR c.e.demo.service.MediaCrawlerClient - ��ȡweibo����ʧ�ܣ�Retries exhausted: 3/3
2025-07-18 14:34:45 [scheduling-1] WARN  c.e.d.service.DataCollectionService - ����΢��Ʒ�Ƽ�زɼ�ʧ�ܻ�������
2025-07-18 14:34:45 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        mc1_0.id,
        mc1_0.created_time,
        mc1_0.extra_config,
        mc1_0.interval_seconds,
        mc1_0.is_enabled,
        mc1_0.keywords,
        mc1_0.last_execution_time,
        mc1_0.name,
        mc1_0.next_execution_time,
        mc1_0.platform,
        mc1_0.updated_time 
    from
        monitor_config mc1_0 
    where
        mc1_0.id=?
2025-07-18 14:34:45 [scheduling-1] DEBUG org.hibernate.SQL - 
    update
        monitor_config 
    set
        created_time=?,
        extra_config=?,
        interval_seconds=?,
        is_enabled=?,
        keywords=?,
        last_execution_time=?,
        name=?,
        next_execution_time=?,
        platform=?,
        updated_time=? 
    where
        id=?
2025-07-18 14:35:33 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - ��ʼִ�����ݲɼ�����
2025-07-18 14:35:33 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        mc1_0.id,
        mc1_0.created_time,
        mc1_0.extra_config,
        mc1_0.interval_seconds,
        mc1_0.is_enabled,
        mc1_0.keywords,
        mc1_0.last_execution_time,
        mc1_0.name,
        mc1_0.next_execution_time,
        mc1_0.platform,
        mc1_0.updated_time 
    from
        monitor_config mc1_0 
    where
        mc1_0.is_enabled 
        and mc1_0.next_execution_time<?
2025-07-18 14:35:33 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - û����Ҫִ�еļ������
2025-07-18 14:36:33 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - ��ʼִ�����ݲɼ�����
2025-07-18 14:36:33 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        mc1_0.id,
        mc1_0.created_time,
        mc1_0.extra_config,
        mc1_0.interval_seconds,
        mc1_0.is_enabled,
        mc1_0.keywords,
        mc1_0.last_execution_time,
        mc1_0.name,
        mc1_0.next_execution_time,
        mc1_0.platform,
        mc1_0.updated_time 
    from
        monitor_config mc1_0 
    where
        mc1_0.is_enabled 
        and mc1_0.next_execution_time<?
2025-07-18 14:36:33 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - û����Ҫִ�еļ������
2025-07-18 14:37:33 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - ��ʼִ�����ݲɼ�����
2025-07-18 14:37:33 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        mc1_0.id,
        mc1_0.created_time,
        mc1_0.extra_config,
        mc1_0.interval_seconds,
        mc1_0.is_enabled,
        mc1_0.keywords,
        mc1_0.last_execution_time,
        mc1_0.name,
        mc1_0.next_execution_time,
        mc1_0.platform,
        mc1_0.updated_time 
    from
        monitor_config mc1_0 
    where
        mc1_0.is_enabled 
        and mc1_0.next_execution_time<?
2025-07-18 14:37:33 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - û����Ҫִ�еļ������
2025-07-18 14:38:33 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - ��ʼִ�����ݲɼ�����
2025-07-18 14:38:33 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        mc1_0.id,
        mc1_0.created_time,
        mc1_0.extra_config,
        mc1_0.interval_seconds,
        mc1_0.is_enabled,
        mc1_0.keywords,
        mc1_0.last_execution_time,
        mc1_0.name,
        mc1_0.next_execution_time,
        mc1_0.platform,
        mc1_0.updated_time 
    from
        monitor_config mc1_0 
    where
        mc1_0.is_enabled 
        and mc1_0.next_execution_time<?
2025-07-18 14:38:33 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - û����Ҫִ�еļ������
2025-07-18 14:38:33 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - ��ʼִ��Ԥ���������
2025-07-18 14:38:33 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        ar1_0.id,
        ar1_0.created_time,
        ar1_0.description,
        ar1_0.is_enabled,
        ar1_0.keywords,
        ar1_0.last_triggered_time,
        ar1_0.name,
        ar1_0.notification_config,
        ar1_0.notification_methods,
        ar1_0.platforms,
        ar1_0.rule_type,
        ar1_0.threshold_config,
        ar1_0.time_window_minutes,
        ar1_0.trigger_count,
        ar1_0.updated_time 
    from
        alert_rule ar1_0 
    where
        ar1_0.is_enabled
2025-07-18 14:38:33 [scheduling-1] INFO  c.example.demo.service.AlertService - ��ʼ���2��Ԥ������
2025-07-18 14:38:33 [scheduling-1] DEBUG c.example.demo.service.AlertService - ���Ԥ�����򣺸������Ԥ��
2025-07-18 14:38:33 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        sd1_0.id,
        sd1_0.author,
        sd1_0.comment_count,
        sd1_0.content,
        sd1_0.content_id,
        sd1_0.content_type,
        sd1_0.crawl_time,
        sd1_0.extra_data,
        sd1_0.is_alerted,
        sd1_0.is_processed,
        sd1_0.like_count,
        sd1_0.matched_keywords,
        sd1_0.platform,
        sd1_0.publish_time,
        sd1_0.sentiment,
        sd1_0.sentiment_score,
        sd1_0.share_count,
        sd1_0.source_url,
        sd1_0.title,
        sd1_0.view_count 
    from
        sentiment_data sd1_0 
    where
        sd1_0.sentiment=? 
        and not(sd1_0.is_alerted)
2025-07-18 14:38:33 [scheduling-1] DEBUG c.example.demo.service.AlertService - ���Ԥ�����򣺹ؼ����ἰ��Ԥ��
2025-07-18 14:38:33 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        sd1_0.id,
        sd1_0.author,
        sd1_0.comment_count,
        sd1_0.content,
        sd1_0.content_id,
        sd1_0.content_type,
        sd1_0.crawl_time,
        sd1_0.extra_data,
        sd1_0.is_alerted,
        sd1_0.is_processed,
        sd1_0.like_count,
        sd1_0.matched_keywords,
        sd1_0.platform,
        sd1_0.publish_time,
        sd1_0.sentiment,
        sd1_0.sentiment_score,
        sd1_0.share_count,
        sd1_0.source_url,
        sd1_0.title,
        sd1_0.view_count 
    from
        sentiment_data sd1_0 
    where
        sd1_0.content like ? escape '' 
        or sd1_0.title like ? escape ''
2025-07-18 14:38:33 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        sd1_0.id,
        sd1_0.author,
        sd1_0.comment_count,
        sd1_0.content,
        sd1_0.content_id,
        sd1_0.content_type,
        sd1_0.crawl_time,
        sd1_0.extra_data,
        sd1_0.is_alerted,
        sd1_0.is_processed,
        sd1_0.like_count,
        sd1_0.matched_keywords,
        sd1_0.platform,
        sd1_0.publish_time,
        sd1_0.sentiment,
        sd1_0.sentiment_score,
        sd1_0.share_count,
        sd1_0.source_url,
        sd1_0.title,
        sd1_0.view_count 
    from
        sentiment_data sd1_0 
    where
        sd1_0.content like ? escape '' 
        or sd1_0.title like ? escape ''
2025-07-18 14:39:33 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - ��ʼִ�����ݲɼ�����
2025-07-18 14:39:33 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        mc1_0.id,
        mc1_0.created_time,
        mc1_0.extra_config,
        mc1_0.interval_seconds,
        mc1_0.is_enabled,
        mc1_0.keywords,
        mc1_0.last_execution_time,
        mc1_0.name,
        mc1_0.next_execution_time,
        mc1_0.platform,
        mc1_0.updated_time 
    from
        monitor_config mc1_0 
    where
        mc1_0.is_enabled 
        and mc1_0.next_execution_time<?
2025-07-18 14:39:33 [scheduling-1] INFO  c.e.d.service.ScheduledTaskService - �ҵ�1����Ҫִ�еļ������
2025-07-18 14:39:33 [scheduling-1] INFO  c.e.d.service.DataCollectionService - ��ʼ�������òɼ����ݣ�����Ʒ�Ƽ��
2025-07-18 14:39:33 [scheduling-1] INFO  c.e.demo.service.MediaCrawlerClient - ��ʼ��ȡdouyin���ݣ��ؼ��ʣ�[Ʒ������, ��Ʒ����]�����ƣ�50
2025-07-18 14:39:39 [reactor-http-nio-6] ERROR c.e.demo.service.MediaCrawlerClient - ��ȡdouyin����ʧ�ܣ�Retries exhausted: 3/3
2025-07-18 14:39:39 [scheduling-1] WARN  c.e.d.service.DataCollectionService - ���ö���Ʒ�Ƽ�زɼ�ʧ�ܻ�������
2025-07-18 14:39:39 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        mc1_0.id,
        mc1_0.created_time,
        mc1_0.extra_config,
        mc1_0.interval_seconds,
        mc1_0.is_enabled,
        mc1_0.keywords,
        mc1_0.last_execution_time,
        mc1_0.name,
        mc1_0.next_execution_time,
        mc1_0.platform,
        mc1_0.updated_time 
    from
        monitor_config mc1_0 
    where
        mc1_0.id=?
2025-07-18 14:39:39 [scheduling-1] DEBUG org.hibernate.SQL - 
    update
        monitor_config 
    set
        created_time=?,
        extra_config=?,
        interval_seconds=?,
        is_enabled=?,
        keywords=?,
        last_execution_time=?,
        name=?,
        next_execution_time=?,
        platform=?,
        updated_time=? 
    where
        id=?
2025-07-18 14:40:33 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - ��ʼִ�����ݲɼ�����
2025-07-18 14:40:33 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        mc1_0.id,
        mc1_0.created_time,
        mc1_0.extra_config,
        mc1_0.interval_seconds,
        mc1_0.is_enabled,
        mc1_0.keywords,
        mc1_0.last_execution_time,
        mc1_0.name,
        mc1_0.next_execution_time,
        mc1_0.platform,
        mc1_0.updated_time 
    from
        monitor_config mc1_0 
    where
        mc1_0.is_enabled 
        and mc1_0.next_execution_time<?
2025-07-18 14:40:33 [scheduling-1] INFO  c.e.d.service.ScheduledTaskService - �ҵ�2����Ҫִ�еļ������
2025-07-18 14:40:33 [scheduling-1] INFO  c.e.d.service.DataCollectionService - ��ʼ�������òɼ����ݣ�С����Ʒ�Ƽ��
2025-07-18 14:40:33 [scheduling-1] INFO  c.e.demo.service.MediaCrawlerClient - ��ʼ��ȡxiaohongshu���ݣ��ؼ��ʣ�[Ʒ������, ��Ʒ����]�����ƣ�50
2025-07-18 14:40:39 [reactor-http-nio-2] ERROR c.e.demo.service.MediaCrawlerClient - ��ȡxiaohongshu����ʧ�ܣ�Retries exhausted: 3/3
2025-07-18 14:40:39 [scheduling-1] WARN  c.e.d.service.DataCollectionService - ����С����Ʒ�Ƽ�زɼ�ʧ�ܻ�������
2025-07-18 14:40:39 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        mc1_0.id,
        mc1_0.created_time,
        mc1_0.extra_config,
        mc1_0.interval_seconds,
        mc1_0.is_enabled,
        mc1_0.keywords,
        mc1_0.last_execution_time,
        mc1_0.name,
        mc1_0.next_execution_time,
        mc1_0.platform,
        mc1_0.updated_time 
    from
        monitor_config mc1_0 
    where
        mc1_0.id=?
2025-07-18 14:40:39 [scheduling-1] DEBUG org.hibernate.SQL - 
    update
        monitor_config 
    set
        created_time=?,
        extra_config=?,
        interval_seconds=?,
        is_enabled=?,
        keywords=?,
        last_execution_time=?,
        name=?,
        next_execution_time=?,
        platform=?,
        updated_time=? 
    where
        id=?
2025-07-18 14:40:39 [scheduling-1] INFO  c.e.d.service.DataCollectionService - ��ʼ�������òɼ����ݣ�΢��Ʒ�Ƽ��
2025-07-18 14:40:39 [scheduling-1] INFO  c.e.demo.service.MediaCrawlerClient - ��ʼ��ȡweibo���ݣ��ؼ��ʣ�[Ʒ������, ��Ʒ����]�����ƣ�50
2025-07-18 14:40:45 [reactor-http-nio-6] ERROR c.e.demo.service.MediaCrawlerClient - ��ȡweibo����ʧ�ܣ�Retries exhausted: 3/3
2025-07-18 14:40:45 [scheduling-1] WARN  c.e.d.service.DataCollectionService - ����΢��Ʒ�Ƽ�زɼ�ʧ�ܻ�������
2025-07-18 14:40:45 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        mc1_0.id,
        mc1_0.created_time,
        mc1_0.extra_config,
        mc1_0.interval_seconds,
        mc1_0.is_enabled,
        mc1_0.keywords,
        mc1_0.last_execution_time,
        mc1_0.name,
        mc1_0.next_execution_time,
        mc1_0.platform,
        mc1_0.updated_time 
    from
        monitor_config mc1_0 
    where
        mc1_0.id=?
2025-07-18 14:40:45 [scheduling-1] DEBUG org.hibernate.SQL - 
    update
        monitor_config 
    set
        created_time=?,
        extra_config=?,
        interval_seconds=?,
        is_enabled=?,
        keywords=?,
        last_execution_time=?,
        name=?,
        next_execution_time=?,
        platform=?,
        updated_time=? 
    where
        id=?
2025-07-18 14:41:33 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - ��ʼִ�����ݲɼ�����
2025-07-18 14:41:33 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        mc1_0.id,
        mc1_0.created_time,
        mc1_0.extra_config,
        mc1_0.interval_seconds,
        mc1_0.is_enabled,
        mc1_0.keywords,
        mc1_0.last_execution_time,
        mc1_0.name,
        mc1_0.next_execution_time,
        mc1_0.platform,
        mc1_0.updated_time 
    from
        monitor_config mc1_0 
    where
        mc1_0.is_enabled 
        and mc1_0.next_execution_time<?
2025-07-18 14:41:33 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - û����Ҫִ�еļ������
2025-07-18 14:42:33 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - ��ʼִ�����ݲɼ�����
2025-07-18 14:42:33 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        mc1_0.id,
        mc1_0.created_time,
        mc1_0.extra_config,
        mc1_0.interval_seconds,
        mc1_0.is_enabled,
        mc1_0.keywords,
        mc1_0.last_execution_time,
        mc1_0.name,
        mc1_0.next_execution_time,
        mc1_0.platform,
        mc1_0.updated_time 
    from
        monitor_config mc1_0 
    where
        mc1_0.is_enabled 
        and mc1_0.next_execution_time<?
2025-07-18 14:42:33 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - û����Ҫִ�еļ������
2025-07-18 14:43:33 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - ��ʼִ�����ݲɼ�����
2025-07-18 14:43:33 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        mc1_0.id,
        mc1_0.created_time,
        mc1_0.extra_config,
        mc1_0.interval_seconds,
        mc1_0.is_enabled,
        mc1_0.keywords,
        mc1_0.last_execution_time,
        mc1_0.name,
        mc1_0.next_execution_time,
        mc1_0.platform,
        mc1_0.updated_time 
    from
        monitor_config mc1_0 
    where
        mc1_0.is_enabled 
        and mc1_0.next_execution_time<?
2025-07-18 14:43:33 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - û����Ҫִ�еļ������
2025-07-18 14:43:33 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - ��ʼִ��Ԥ���������
2025-07-18 14:43:33 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        ar1_0.id,
        ar1_0.created_time,
        ar1_0.description,
        ar1_0.is_enabled,
        ar1_0.keywords,
        ar1_0.last_triggered_time,
        ar1_0.name,
        ar1_0.notification_config,
        ar1_0.notification_methods,
        ar1_0.platforms,
        ar1_0.rule_type,
        ar1_0.threshold_config,
        ar1_0.time_window_minutes,
        ar1_0.trigger_count,
        ar1_0.updated_time 
    from
        alert_rule ar1_0 
    where
        ar1_0.is_enabled
2025-07-18 14:43:33 [scheduling-1] INFO  c.example.demo.service.AlertService - ��ʼ���2��Ԥ������
2025-07-18 14:43:33 [scheduling-1] DEBUG c.example.demo.service.AlertService - ���Ԥ�����򣺸������Ԥ��
2025-07-18 14:43:33 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        sd1_0.id,
        sd1_0.author,
        sd1_0.comment_count,
        sd1_0.content,
        sd1_0.content_id,
        sd1_0.content_type,
        sd1_0.crawl_time,
        sd1_0.extra_data,
        sd1_0.is_alerted,
        sd1_0.is_processed,
        sd1_0.like_count,
        sd1_0.matched_keywords,
        sd1_0.platform,
        sd1_0.publish_time,
        sd1_0.sentiment,
        sd1_0.sentiment_score,
        sd1_0.share_count,
        sd1_0.source_url,
        sd1_0.title,
        sd1_0.view_count 
    from
        sentiment_data sd1_0 
    where
        sd1_0.sentiment=? 
        and not(sd1_0.is_alerted)
2025-07-18 14:43:33 [scheduling-1] DEBUG c.example.demo.service.AlertService - ���Ԥ�����򣺹ؼ����ἰ��Ԥ��
2025-07-18 14:43:33 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        sd1_0.id,
        sd1_0.author,
        sd1_0.comment_count,
        sd1_0.content,
        sd1_0.content_id,
        sd1_0.content_type,
        sd1_0.crawl_time,
        sd1_0.extra_data,
        sd1_0.is_alerted,
        sd1_0.is_processed,
        sd1_0.like_count,
        sd1_0.matched_keywords,
        sd1_0.platform,
        sd1_0.publish_time,
        sd1_0.sentiment,
        sd1_0.sentiment_score,
        sd1_0.share_count,
        sd1_0.source_url,
        sd1_0.title,
        sd1_0.view_count 
    from
        sentiment_data sd1_0 
    where
        sd1_0.content like ? escape '' 
        or sd1_0.title like ? escape ''
2025-07-18 14:43:33 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        sd1_0.id,
        sd1_0.author,
        sd1_0.comment_count,
        sd1_0.content,
        sd1_0.content_id,
        sd1_0.content_type,
        sd1_0.crawl_time,
        sd1_0.extra_data,
        sd1_0.is_alerted,
        sd1_0.is_processed,
        sd1_0.like_count,
        sd1_0.matched_keywords,
        sd1_0.platform,
        sd1_0.publish_time,
        sd1_0.sentiment,
        sd1_0.sentiment_score,
        sd1_0.share_count,
        sd1_0.source_url,
        sd1_0.title,
        sd1_0.view_count 
    from
        sentiment_data sd1_0 
    where
        sd1_0.content like ? escape '' 
        or sd1_0.title like ? escape ''
2025-07-18 14:44:33 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - ��ʼִ�����ݲɼ�����
2025-07-18 14:44:33 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        mc1_0.id,
        mc1_0.created_time,
        mc1_0.extra_config,
        mc1_0.interval_seconds,
        mc1_0.is_enabled,
        mc1_0.keywords,
        mc1_0.last_execution_time,
        mc1_0.name,
        mc1_0.next_execution_time,
        mc1_0.platform,
        mc1_0.updated_time 
    from
        monitor_config mc1_0 
    where
        mc1_0.is_enabled 
        and mc1_0.next_execution_time<?
2025-07-18 14:44:33 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - û����Ҫִ�еļ������
2025-07-18 14:45:33 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - ��ʼִ�����ݲɼ�����
2025-07-18 14:45:33 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        mc1_0.id,
        mc1_0.created_time,
        mc1_0.extra_config,
        mc1_0.interval_seconds,
        mc1_0.is_enabled,
        mc1_0.keywords,
        mc1_0.last_execution_time,
        mc1_0.name,
        mc1_0.next_execution_time,
        mc1_0.platform,
        mc1_0.updated_time 
    from
        monitor_config mc1_0 
    where
        mc1_0.is_enabled 
        and mc1_0.next_execution_time<?
2025-07-18 14:45:33 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - û����Ҫִ�еļ������
2025-07-18 14:46:33 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - ��ʼִ�����ݲɼ�����
2025-07-18 14:46:33 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        mc1_0.id,
        mc1_0.created_time,
        mc1_0.extra_config,
        mc1_0.interval_seconds,
        mc1_0.is_enabled,
        mc1_0.keywords,
        mc1_0.last_execution_time,
        mc1_0.name,
        mc1_0.next_execution_time,
        mc1_0.platform,
        mc1_0.updated_time 
    from
        monitor_config mc1_0 
    where
        mc1_0.is_enabled 
        and mc1_0.next_execution_time<?
2025-07-18 14:46:33 [scheduling-1] INFO  c.e.d.service.ScheduledTaskService - �ҵ�2����Ҫִ�еļ������
2025-07-18 14:46:33 [scheduling-1] INFO  c.e.d.service.DataCollectionService - ��ʼ�������òɼ����ݣ�С����Ʒ�Ƽ��
2025-07-18 14:46:33 [scheduling-1] INFO  c.e.demo.service.MediaCrawlerClient - ��ʼ��ȡxiaohongshu���ݣ��ؼ��ʣ�[Ʒ������, ��Ʒ����]�����ƣ�50
2025-07-18 14:46:39 [reactor-http-nio-2] ERROR c.e.demo.service.MediaCrawlerClient - ��ȡxiaohongshu����ʧ�ܣ�Retries exhausted: 3/3
2025-07-18 14:46:39 [scheduling-1] WARN  c.e.d.service.DataCollectionService - ����С����Ʒ�Ƽ�زɼ�ʧ�ܻ�������
2025-07-18 14:46:39 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        mc1_0.id,
        mc1_0.created_time,
        mc1_0.extra_config,
        mc1_0.interval_seconds,
        mc1_0.is_enabled,
        mc1_0.keywords,
        mc1_0.last_execution_time,
        mc1_0.name,
        mc1_0.next_execution_time,
        mc1_0.platform,
        mc1_0.updated_time 
    from
        monitor_config mc1_0 
    where
        mc1_0.id=?
2025-07-18 14:46:39 [scheduling-1] DEBUG org.hibernate.SQL - 
    update
        monitor_config 
    set
        created_time=?,
        extra_config=?,
        interval_seconds=?,
        is_enabled=?,
        keywords=?,
        last_execution_time=?,
        name=?,
        next_execution_time=?,
        platform=?,
        updated_time=? 
    where
        id=?
2025-07-18 14:46:39 [scheduling-1] INFO  c.e.d.service.DataCollectionService - ��ʼ�������òɼ����ݣ�΢��Ʒ�Ƽ��
2025-07-18 14:46:39 [scheduling-1] INFO  c.e.demo.service.MediaCrawlerClient - ��ʼ��ȡweibo���ݣ��ؼ��ʣ�[Ʒ������, ��Ʒ����]�����ƣ�50
2025-07-18 14:46:45 [reactor-http-nio-6] ERROR c.e.demo.service.MediaCrawlerClient - ��ȡweibo����ʧ�ܣ�Retries exhausted: 3/3
2025-07-18 14:46:45 [scheduling-1] WARN  c.e.d.service.DataCollectionService - ����΢��Ʒ�Ƽ�زɼ�ʧ�ܻ�������
2025-07-18 14:46:45 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        mc1_0.id,
        mc1_0.created_time,
        mc1_0.extra_config,
        mc1_0.interval_seconds,
        mc1_0.is_enabled,
        mc1_0.keywords,
        mc1_0.last_execution_time,
        mc1_0.name,
        mc1_0.next_execution_time,
        mc1_0.platform,
        mc1_0.updated_time 
    from
        monitor_config mc1_0 
    where
        mc1_0.id=?
2025-07-18 14:46:45 [scheduling-1] DEBUG org.hibernate.SQL - 
    update
        monitor_config 
    set
        created_time=?,
        extra_config=?,
        interval_seconds=?,
        is_enabled=?,
        keywords=?,
        last_execution_time=?,
        name=?,
        next_execution_time=?,
        platform=?,
        updated_time=? 
    where
        id=?
2025-07-18 14:47:33 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - ��ʼִ�����ݲɼ�����
2025-07-18 14:47:33 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        mc1_0.id,
        mc1_0.created_time,
        mc1_0.extra_config,
        mc1_0.interval_seconds,
        mc1_0.is_enabled,
        mc1_0.keywords,
        mc1_0.last_execution_time,
        mc1_0.name,
        mc1_0.next_execution_time,
        mc1_0.platform,
        mc1_0.updated_time 
    from
        monitor_config mc1_0 
    where
        mc1_0.is_enabled 
        and mc1_0.next_execution_time<?
2025-07-18 14:47:33 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - û����Ҫִ�еļ������
2025-07-18 14:48:33 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - ��ʼִ�����ݲɼ�����
2025-07-18 14:48:33 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        mc1_0.id,
        mc1_0.created_time,
        mc1_0.extra_config,
        mc1_0.interval_seconds,
        mc1_0.is_enabled,
        mc1_0.keywords,
        mc1_0.last_execution_time,
        mc1_0.name,
        mc1_0.next_execution_time,
        mc1_0.platform,
        mc1_0.updated_time 
    from
        monitor_config mc1_0 
    where
        mc1_0.is_enabled 
        and mc1_0.next_execution_time<?
2025-07-18 14:48:33 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - û����Ҫִ�еļ������
2025-07-18 14:48:33 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - ��ʼִ��Ԥ���������
2025-07-18 14:48:33 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        ar1_0.id,
        ar1_0.created_time,
        ar1_0.description,
        ar1_0.is_enabled,
        ar1_0.keywords,
        ar1_0.last_triggered_time,
        ar1_0.name,
        ar1_0.notification_config,
        ar1_0.notification_methods,
        ar1_0.platforms,
        ar1_0.rule_type,
        ar1_0.threshold_config,
        ar1_0.time_window_minutes,
        ar1_0.trigger_count,
        ar1_0.updated_time 
    from
        alert_rule ar1_0 
    where
        ar1_0.is_enabled
2025-07-18 14:48:33 [scheduling-1] INFO  c.example.demo.service.AlertService - ��ʼ���2��Ԥ������
2025-07-18 14:48:33 [scheduling-1] DEBUG c.example.demo.service.AlertService - ���Ԥ�����򣺸������Ԥ��
2025-07-18 14:48:33 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        sd1_0.id,
        sd1_0.author,
        sd1_0.comment_count,
        sd1_0.content,
        sd1_0.content_id,
        sd1_0.content_type,
        sd1_0.crawl_time,
        sd1_0.extra_data,
        sd1_0.is_alerted,
        sd1_0.is_processed,
        sd1_0.like_count,
        sd1_0.matched_keywords,
        sd1_0.platform,
        sd1_0.publish_time,
        sd1_0.sentiment,
        sd1_0.sentiment_score,
        sd1_0.share_count,
        sd1_0.source_url,
        sd1_0.title,
        sd1_0.view_count 
    from
        sentiment_data sd1_0 
    where
        sd1_0.sentiment=? 
        and not(sd1_0.is_alerted)
2025-07-18 14:48:33 [scheduling-1] DEBUG c.example.demo.service.AlertService - ���Ԥ�����򣺹ؼ����ἰ��Ԥ��
2025-07-18 14:48:33 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        sd1_0.id,
        sd1_0.author,
        sd1_0.comment_count,
        sd1_0.content,
        sd1_0.content_id,
        sd1_0.content_type,
        sd1_0.crawl_time,
        sd1_0.extra_data,
        sd1_0.is_alerted,
        sd1_0.is_processed,
        sd1_0.like_count,
        sd1_0.matched_keywords,
        sd1_0.platform,
        sd1_0.publish_time,
        sd1_0.sentiment,
        sd1_0.sentiment_score,
        sd1_0.share_count,
        sd1_0.source_url,
        sd1_0.title,
        sd1_0.view_count 
    from
        sentiment_data sd1_0 
    where
        sd1_0.content like ? escape '' 
        or sd1_0.title like ? escape ''
2025-07-18 14:48:33 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        sd1_0.id,
        sd1_0.author,
        sd1_0.comment_count,
        sd1_0.content,
        sd1_0.content_id,
        sd1_0.content_type,
        sd1_0.crawl_time,
        sd1_0.extra_data,
        sd1_0.is_alerted,
        sd1_0.is_processed,
        sd1_0.like_count,
        sd1_0.matched_keywords,
        sd1_0.platform,
        sd1_0.publish_time,
        sd1_0.sentiment,
        sd1_0.sentiment_score,
        sd1_0.share_count,
        sd1_0.source_url,
        sd1_0.title,
        sd1_0.view_count 
    from
        sentiment_data sd1_0 
    where
        sd1_0.content like ? escape '' 
        or sd1_0.title like ? escape ''
2025-07-18 14:49:33 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - ��ʼִ�����ݲɼ�����
2025-07-18 14:49:33 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        mc1_0.id,
        mc1_0.created_time,
        mc1_0.extra_config,
        mc1_0.interval_seconds,
        mc1_0.is_enabled,
        mc1_0.keywords,
        mc1_0.last_execution_time,
        mc1_0.name,
        mc1_0.next_execution_time,
        mc1_0.platform,
        mc1_0.updated_time 
    from
        monitor_config mc1_0 
    where
        mc1_0.is_enabled 
        and mc1_0.next_execution_time<?
2025-07-18 14:49:33 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - û����Ҫִ�еļ������
2025-07-18 14:50:33 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - ��ʼִ�����ݲɼ�����
2025-07-18 14:50:33 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        mc1_0.id,
        mc1_0.created_time,
        mc1_0.extra_config,
        mc1_0.interval_seconds,
        mc1_0.is_enabled,
        mc1_0.keywords,
        mc1_0.last_execution_time,
        mc1_0.name,
        mc1_0.next_execution_time,
        mc1_0.platform,
        mc1_0.updated_time 
    from
        monitor_config mc1_0 
    where
        mc1_0.is_enabled 
        and mc1_0.next_execution_time<?
2025-07-18 14:50:33 [scheduling-1] INFO  c.e.d.service.ScheduledTaskService - �ҵ�1����Ҫִ�еļ������
2025-07-18 14:50:33 [scheduling-1] INFO  c.e.d.service.DataCollectionService - ��ʼ�������òɼ����ݣ�����Ʒ�Ƽ��
2025-07-18 14:50:33 [scheduling-1] INFO  c.e.demo.service.MediaCrawlerClient - ��ʼ��ȡdouyin���ݣ��ؼ��ʣ�[Ʒ������, ��Ʒ����]�����ƣ�50
2025-07-18 14:50:39 [reactor-http-nio-2] ERROR c.e.demo.service.MediaCrawlerClient - ��ȡdouyin����ʧ�ܣ�Retries exhausted: 3/3
2025-07-18 14:50:39 [scheduling-1] WARN  c.e.d.service.DataCollectionService - ���ö���Ʒ�Ƽ�زɼ�ʧ�ܻ�������
2025-07-18 14:50:39 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        mc1_0.id,
        mc1_0.created_time,
        mc1_0.extra_config,
        mc1_0.interval_seconds,
        mc1_0.is_enabled,
        mc1_0.keywords,
        mc1_0.last_execution_time,
        mc1_0.name,
        mc1_0.next_execution_time,
        mc1_0.platform,
        mc1_0.updated_time 
    from
        monitor_config mc1_0 
    where
        mc1_0.id=?
2025-07-18 14:50:39 [scheduling-1] DEBUG org.hibernate.SQL - 
    update
        monitor_config 
    set
        created_time=?,
        extra_config=?,
        interval_seconds=?,
        is_enabled=?,
        keywords=?,
        last_execution_time=?,
        name=?,
        next_execution_time=?,
        platform=?,
        updated_time=? 
    where
        id=?
2025-07-18 14:51:33 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - ��ʼִ�����ݲɼ�����
2025-07-18 14:51:33 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        mc1_0.id,
        mc1_0.created_time,
        mc1_0.extra_config,
        mc1_0.interval_seconds,
        mc1_0.is_enabled,
        mc1_0.keywords,
        mc1_0.last_execution_time,
        mc1_0.name,
        mc1_0.next_execution_time,
        mc1_0.platform,
        mc1_0.updated_time 
    from
        monitor_config mc1_0 
    where
        mc1_0.is_enabled 
        and mc1_0.next_execution_time<?
2025-07-18 14:51:33 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - û����Ҫִ�еļ������
2025-07-18 14:52:33 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - ��ʼִ�����ݲɼ�����
2025-07-18 14:52:33 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        mc1_0.id,
        mc1_0.created_time,
        mc1_0.extra_config,
        mc1_0.interval_seconds,
        mc1_0.is_enabled,
        mc1_0.keywords,
        mc1_0.last_execution_time,
        mc1_0.name,
        mc1_0.next_execution_time,
        mc1_0.platform,
        mc1_0.updated_time 
    from
        monitor_config mc1_0 
    where
        mc1_0.is_enabled 
        and mc1_0.next_execution_time<?
2025-07-18 14:52:33 [scheduling-1] INFO  c.e.d.service.ScheduledTaskService - �ҵ�2����Ҫִ�еļ������
2025-07-18 14:52:33 [scheduling-1] INFO  c.e.d.service.DataCollectionService - ��ʼ�������òɼ����ݣ�С����Ʒ�Ƽ��
2025-07-18 14:52:33 [scheduling-1] INFO  c.e.demo.service.MediaCrawlerClient - ��ʼ��ȡxiaohongshu���ݣ��ؼ��ʣ�[Ʒ������, ��Ʒ����]�����ƣ�50
2025-07-18 14:52:39 [reactor-http-nio-6] ERROR c.e.demo.service.MediaCrawlerClient - ��ȡxiaohongshu����ʧ�ܣ�Retries exhausted: 3/3
2025-07-18 14:52:39 [scheduling-1] WARN  c.e.d.service.DataCollectionService - ����С����Ʒ�Ƽ�زɼ�ʧ�ܻ�������
2025-07-18 14:52:39 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        mc1_0.id,
        mc1_0.created_time,
        mc1_0.extra_config,
        mc1_0.interval_seconds,
        mc1_0.is_enabled,
        mc1_0.keywords,
        mc1_0.last_execution_time,
        mc1_0.name,
        mc1_0.next_execution_time,
        mc1_0.platform,
        mc1_0.updated_time 
    from
        monitor_config mc1_0 
    where
        mc1_0.id=?
2025-07-18 14:52:39 [scheduling-1] DEBUG org.hibernate.SQL - 
    update
        monitor_config 
    set
        created_time=?,
        extra_config=?,
        interval_seconds=?,
        is_enabled=?,
        keywords=?,
        last_execution_time=?,
        name=?,
        next_execution_time=?,
        platform=?,
        updated_time=? 
    where
        id=?
2025-07-18 14:52:39 [scheduling-1] INFO  c.e.d.service.DataCollectionService - ��ʼ�������òɼ����ݣ�΢��Ʒ�Ƽ��
2025-07-18 14:52:39 [scheduling-1] INFO  c.e.demo.service.MediaCrawlerClient - ��ʼ��ȡweibo���ݣ��ؼ��ʣ�[Ʒ������, ��Ʒ����]�����ƣ�50
2025-07-18 14:52:45 [reactor-http-nio-2] ERROR c.e.demo.service.MediaCrawlerClient - ��ȡweibo����ʧ�ܣ�Retries exhausted: 3/3
2025-07-18 14:52:45 [scheduling-1] WARN  c.e.d.service.DataCollectionService - ����΢��Ʒ�Ƽ�زɼ�ʧ�ܻ�������
2025-07-18 14:52:45 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        mc1_0.id,
        mc1_0.created_time,
        mc1_0.extra_config,
        mc1_0.interval_seconds,
        mc1_0.is_enabled,
        mc1_0.keywords,
        mc1_0.last_execution_time,
        mc1_0.name,
        mc1_0.next_execution_time,
        mc1_0.platform,
        mc1_0.updated_time 
    from
        monitor_config mc1_0 
    where
        mc1_0.id=?
2025-07-18 14:52:45 [scheduling-1] DEBUG org.hibernate.SQL - 
    update
        monitor_config 
    set
        created_time=?,
        extra_config=?,
        interval_seconds=?,
        is_enabled=?,
        keywords=?,
        last_execution_time=?,
        name=?,
        next_execution_time=?,
        platform=?,
        updated_time=? 
    where
        id=?
2025-07-18 14:53:33 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - ��ʼִ�����ݲɼ�����
2025-07-18 14:53:33 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        mc1_0.id,
        mc1_0.created_time,
        mc1_0.extra_config,
        mc1_0.interval_seconds,
        mc1_0.is_enabled,
        mc1_0.keywords,
        mc1_0.last_execution_time,
        mc1_0.name,
        mc1_0.next_execution_time,
        mc1_0.platform,
        mc1_0.updated_time 
    from
        monitor_config mc1_0 
    where
        mc1_0.is_enabled 
        and mc1_0.next_execution_time<?
2025-07-18 14:53:33 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - û����Ҫִ�еļ������
2025-07-18 14:53:33 [scheduling-1] DEBUG c.e.d.service.ScheduledTaskService - ��ʼִ��Ԥ���������
2025-07-18 14:53:33 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        ar1_0.id,
        ar1_0.created_time,
        ar1_0.description,
        ar1_0.is_enabled,
        ar1_0.keywords,
        ar1_0.last_triggered_time,
        ar1_0.name,
        ar1_0.notification_config,
        ar1_0.notification_methods,
        ar1_0.platforms,
        ar1_0.rule_type,
        ar1_0.threshold_config,
        ar1_0.time_window_minutes,
        ar1_0.trigger_count,
        ar1_0.updated_time 
    from
        alert_rule ar1_0 
    where
        ar1_0.is_enabled
2025-07-18 14:53:33 [scheduling-1] INFO  c.example.demo.service.AlertService - ��ʼ���2��Ԥ������
2025-07-18 14:53:33 [scheduling-1] DEBUG c.example.demo.service.AlertService - ���Ԥ�����򣺸������Ԥ��
2025-07-18 14:53:33 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        sd1_0.id,
        sd1_0.author,
        sd1_0.comment_count,
        sd1_0.content,
        sd1_0.content_id,
        sd1_0.content_type,
        sd1_0.crawl_time,
        sd1_0.extra_data,
        sd1_0.is_alerted,
        sd1_0.is_processed,
        sd1_0.like_count,
        sd1_0.matched_keywords,
        sd1_0.platform,
        sd1_0.publish_time,
        sd1_0.sentiment,
        sd1_0.sentiment_score,
        sd1_0.share_count,
        sd1_0.source_url,
        sd1_0.title,
        sd1_0.view_count 
    from
        sentiment_data sd1_0 
    where
        sd1_0.sentiment=? 
        and not(sd1_0.is_alerted)
2025-07-18 14:53:33 [scheduling-1] DEBUG c.example.demo.service.AlertService - ���Ԥ�����򣺹ؼ����ἰ��Ԥ��
2025-07-18 14:53:33 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        sd1_0.id,
        sd1_0.author,
        sd1_0.comment_count,
        sd1_0.content,
        sd1_0.content_id,
        sd1_0.content_type,
        sd1_0.crawl_time,
        sd1_0.extra_data,
        sd1_0.is_alerted,
        sd1_0.is_processed,
        sd1_0.like_count,
        sd1_0.matched_keywords,
        sd1_0.platform,
        sd1_0.publish_time,
        sd1_0.sentiment,
        sd1_0.sentiment_score,
        sd1_0.share_count,
        sd1_0.source_url,
        sd1_0.title,
        sd1_0.view_count 
    from
        sentiment_data sd1_0 
    where
        sd1_0.content like ? escape '' 
        or sd1_0.title like ? escape ''
2025-07-18 14:53:33 [scheduling-1] DEBUG org.hibernate.SQL - 
    select
        sd1_0.id,
        sd1_0.author,
        sd1_0.comment_count,
        sd1_0.content,
        sd1_0.content_id,
        sd1_0.content_type,
        sd1_0.crawl_time,
        sd1_0.extra_data,
        sd1_0.is_alerted,
        sd1_0.is_processed,
        sd1_0.like_count,
        sd1_0.matched_keywords,
        sd1_0.platform,
        sd1_0.publish_time,
        sd1_0.sentiment,
        sd1_0.sentiment_score,
        sd1_0.share_count,
        sd1_0.source_url,
        sd1_0.title,
        sd1_0.view_count 
    from
        sentiment_data sd1_0 
    where
        sd1_0.content like ? escape '' 
        or sd1_0.title like ? escape ''
